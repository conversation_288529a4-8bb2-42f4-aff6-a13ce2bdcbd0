MetaInfo {
    Type {
        name: "QtQuick3D.Effects.AdditiveColorGradient"
        icon: "images/effect16.png"

        Hints {
            visibleInNavigator: true
            canBeDroppedInNavigator: true
            canBeDroppedInFormEditor: false
            canBeDroppedInView3D: false
        }

        ItemLibraryEntry {
            name: "Additive Color Gradient"
            category: "Qt Quick 3D Effects"
            libraryIcon: "images/effect.png"
            version: "1.15"
            requiredImport: "QtQuick3D.Effects"
        }
    }
    Type {
        name: "QtQuick3D.Effects.Blur"
        icon: "images/effect16.png"

        Hints {
            visibleInNavigator: true
            canBeDroppedInNavigator: true
            canBeDroppedInFormEditor: false
            canBeDroppedInView3D: false
        }

        ItemLibraryEntry {
            name: "Blur"
            category: "Qt Quick 3D Effects"
            libraryIcon: "images/effect.png"
            version: "1.15"
            requiredImport: "QtQuick3D.Effects"
        }
    }
    Type {
        name: "QtQuick3D.Effects.BrushStrokes"
        icon: "images/effect16.png"

        Hints {
            visibleInNavigator: true
            canBeDroppedInNavigator: true
            canBeDroppedInFormEditor: false
            canBeDroppedInView3D: false
        }

        ItemLibraryEntry {
            name: "Brush Strokes"
            category: "Qt Quick 3D Effects"
            libraryIcon: "images/effect.png"
            version: "1.15"
            requiredImport: "QtQuick3D.Effects"
        }
    }
    Type {
        name: "QtQuick3D.Effects.ChromaticAberration"
        icon: "images/effect16.png"

        Hints {
            visibleInNavigator: true
            canBeDroppedInNavigator: true
            canBeDroppedInFormEditor: false
            canBeDroppedInView3D: false
        }

        ItemLibraryEntry {
            name: "Chromatic Aberration"
            category: "Qt Quick 3D Effects"
            libraryIcon: "images/effect.png"
            version: "1.15"
            requiredImport: "QtQuick3D.Effects"
        }
    }
    Type {
        name: "QtQuick3D.Effects.ColorMaster"
        icon: "images/effect16.png"

        Hints {
            visibleInNavigator: true
            canBeDroppedInNavigator: true
            canBeDroppedInFormEditor: false
            canBeDroppedInView3D: false
        }

        ItemLibraryEntry {
            name: "Color Master"
            category: "Qt Quick 3D Effects"
            libraryIcon: "images/effect.png"
            version: "1.15"
            requiredImport: "QtQuick3D.Effects"
        }
    }
    Type {
        name: "QtQuick3D.Effects.DepthOfFieldHQBlur"
        icon: "images/effect16.png"

        Hints {
            visibleInNavigator: true
            canBeDroppedInNavigator: true
            canBeDroppedInFormEditor: false
            canBeDroppedInView3D: false
        }

        ItemLibraryEntry {
            name: "Depth of Field HQ Blur"
            category: "Qt Quick 3D Effects"
            libraryIcon: "images/effect.png"
            version: "1.15"
            requiredImport: "QtQuick3D.Effects"
        }
    }
    Type {
        name: "QtQuick3D.Effects.Desaturate"
        icon: "images/effect16.png"

        Hints {
            visibleInNavigator: true
            canBeDroppedInNavigator: true
            canBeDroppedInFormEditor: false
            canBeDroppedInView3D: false
        }

        ItemLibraryEntry {
            name: "Desaturate"
            category: "Qt Quick 3D Effects"
            libraryIcon: "images/effect.png"
            version: "1.15"
            requiredImport: "QtQuick3D.Effects"
        }
    }
    Type {
        name: "QtQuick3D.Effects.DistortionRipple"
        icon: "images/effect16.png"

        Hints {
            visibleInNavigator: true
            canBeDroppedInNavigator: true
            canBeDroppedInFormEditor: false
            canBeDroppedInView3D: false
        }

        ItemLibraryEntry {
            name: "Distortion Ripple"
            category: "Qt Quick 3D Effects"
            libraryIcon: "images/effect.png"
            version: "1.15"
            requiredImport: "QtQuick3D.Effects"
        }
    }
    Type {
        name: "QtQuick3D.Effects.DistortionSphere"
        icon: "images/effect16.png"

        Hints {
            visibleInNavigator: true
            canBeDroppedInNavigator: true
            canBeDroppedInFormEditor: false
            canBeDroppedInView3D: false
        }

        ItemLibraryEntry {
            name: "Distortion Sphere"
            category: "Qt Quick 3D Effects"
            libraryIcon: "images/effect.png"
            version: "1.15"
            requiredImport: "QtQuick3D.Effects"
        }
    }
    Type {
        name: "QtQuick3D.Effects.DistortionSpiral"
        icon: "images/effect16.png"

        Hints {
            visibleInNavigator: true
            canBeDroppedInNavigator: true
            canBeDroppedInFormEditor: false
            canBeDroppedInView3D: false
        }

        ItemLibraryEntry {
            name: "Distortion Spiral"
            category: "Qt Quick 3D Effects"
            libraryIcon: "images/effect.png"
            version: "1.15"
            requiredImport: "QtQuick3D.Effects"
        }
    }
    Type {
        name: "QtQuick3D.Effects.EdgeDetect"
        icon: "images/effect16.png"

        Hints {
            visibleInNavigator: true
            canBeDroppedInNavigator: true
            canBeDroppedInFormEditor: false
            canBeDroppedInView3D: false
        }

        ItemLibraryEntry {
            name: "Edge Detect"
            category: "Qt Quick 3D Effects"
            libraryIcon: "images/effect.png"
            version: "1.15"
            requiredImport: "QtQuick3D.Effects"
        }
    }
    Type {
        name: "QtQuick3D.Effects.Emboss"
        icon: "images/effect16.png"

        Hints {
            visibleInNavigator: true
            canBeDroppedInNavigator: true
            canBeDroppedInFormEditor: false
            canBeDroppedInView3D: false
        }

        ItemLibraryEntry {
            name: "Emboss"
            category: "Qt Quick 3D Effects"
            libraryIcon: "images/effect.png"
            version: "1.15"
            requiredImport: "QtQuick3D.Effects"
        }
    }
    Type {
        name: "QtQuick3D.Effects.Flip"
        icon: "images/effect16.png"

        Hints {
            visibleInNavigator: true
            canBeDroppedInNavigator: true
            canBeDroppedInFormEditor: false
            canBeDroppedInView3D: false
        }

        ItemLibraryEntry {
            name: "Flip"
            category: "Qt Quick 3D Effects"
            libraryIcon: "images/effect.png"
            version: "1.15"
            requiredImport: "QtQuick3D.Effects"
        }
    }
    Type {
        name: "QtQuick3D.Effects.Fxaa"
        icon: "images/effect16.png"

        Hints {
            visibleInNavigator: true
            canBeDroppedInNavigator: true
            canBeDroppedInFormEditor: false
            canBeDroppedInView3D: false
        }

        ItemLibraryEntry {
            name: "Fxaa"
            category: "Qt Quick 3D Effects"
            libraryIcon: "images/effect.png"
            version: "1.15"
            requiredImport: "QtQuick3D.Effects"
        }
    }
    Type {
        name: "QtQuick3D.Effects.GaussianBlur"
        icon: "images/effect16.png"

        Hints {
            visibleInNavigator: true
            canBeDroppedInNavigator: true
            canBeDroppedInFormEditor: false
            canBeDroppedInView3D: false
        }

        ItemLibraryEntry {
            name: "Gaussian Blur"
            category: "Qt Quick 3D Effects"
            libraryIcon: "images/effect.png"
            version: "1.15"
            requiredImport: "QtQuick3D.Effects"
        }
    }
    Type {
        name: "QtQuick3D.Effects.HDRBloomTonemap"
        icon: "images/effect16.png"

        Hints {
            visibleInNavigator: true
            canBeDroppedInNavigator: true
            canBeDroppedInFormEditor: false
            canBeDroppedInView3D: false
        }

        ItemLibraryEntry {
            name: "HDR Bloom Tonemap"
            category: "Qt Quick 3D Effects"
            libraryIcon: "images/effect.png"
            version: "1.15"
            requiredImport: "QtQuick3D.Effects"
        }
    }
    Type {
        name: "QtQuick3D.Effects.MotionBlur"
        icon: "images/effect16.png"

        Hints {
            visibleInNavigator: true
            canBeDroppedInNavigator: true
            canBeDroppedInFormEditor: false
            canBeDroppedInView3D: false
        }

        ItemLibraryEntry {
            name: "Motion Blur"
            category: "Qt Quick 3D Effects"
            libraryIcon: "images/effect.png"
            version: "1.15"
            requiredImport: "QtQuick3D.Effects"
        }
    }
    Type {
        name: "QtQuick3D.Effects.Scatter"
        icon: "images/effect16.png"

        Hints {
            visibleInNavigator: true
            canBeDroppedInNavigator: true
            canBeDroppedInFormEditor: false
            canBeDroppedInView3D: false
        }

        ItemLibraryEntry {
            name: "Scatter"
            category: "Qt Quick 3D Effects"
            libraryIcon: "images/effect.png"
            version: "1.15"
            requiredImport: "QtQuick3D.Effects"
        }
    }
    Type {
        name: "QtQuick3D.Effects.SCurveTonemap"
        icon: "images/effect16.png"

        Hints {
            visibleInNavigator: true
            canBeDroppedInNavigator: true
            canBeDroppedInFormEditor: false
            canBeDroppedInView3D: false
        }

        ItemLibraryEntry {
            name: "SCurve Tonemap"
            category: "Qt Quick 3D Effects"
            libraryIcon: "images/effect.png"
            version: "1.15"
            requiredImport: "QtQuick3D.Effects"
        }
    }
    Type {
        name: "QtQuick3D.Effects.TiltShift"
        icon: "images/effect16.png"

        Hints {
            visibleInNavigator: true
            canBeDroppedInNavigator: true
            canBeDroppedInFormEditor: false
            canBeDroppedInView3D: false
        }

        ItemLibraryEntry {
            name: "Tilt Shift"
            category: "Qt Quick 3D Effects"
            libraryIcon: "images/effect.png"
            version: "1.15"
            requiredImport: "QtQuick3D.Effects"
        }
    }
    Type {
        name: "QtQuick3D.Effects.Vignette"
        icon: "images/effect16.png"

        Hints {
            visibleInNavigator: true
            canBeDroppedInNavigator: true
            canBeDroppedInFormEditor: false
            canBeDroppedInView3D: false
        }

        ItemLibraryEntry {
            name: "Vignette"
            category: "Qt Quick 3D Effects"
            libraryIcon: "images/effect.png"
            version: "1.15"
            requiredImport: "QtQuick3D.Effects"
        }
    }
    Type {
        name: "QtQuick3D.Effects.Effect"
        icon: "images/effect16.png"

        Hints {
            visibleInNavigator: true
            canBeDroppedInNavigator: true
            canBeDroppedInFormEditor: false
        }

        ItemLibraryEntry {
            name: "Effect"
            category: "Qt Quick 3D Custom Shader Utils"
            libraryIcon: "images/effect.png"
            version: "1.15"
            requiredImport: "QtQuick3D.Effects"
            QmlSource { source: "./source/effect_template.qml" }
        }
    }
}
