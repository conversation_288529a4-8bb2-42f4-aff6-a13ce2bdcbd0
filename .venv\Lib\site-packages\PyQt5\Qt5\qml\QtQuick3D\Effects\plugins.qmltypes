import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by:
// 'qmlplugindump -nonrelocatable QtQuick3D.Effects 1.15'

Module {
    dependencies: [
        "QtQuick 2.15",
        "QtQuick.Window 2.1",
        "QtQuick3D 1.15",
        "QtQuick3D.Materials 1.15"
    ]
    Component {
        name: "QQuick3DEffect"
        defaultProperty: "data"
        prototype: "QQuick3DObject"
        exports: ["QtQuick3D.Effects/Effect 1.15"]
        exportMetaObjectRevisions: [0]
        Property { name: "passes"; type: "QQuick3DShaderUtilsRenderPass"; isList: true; isReadonly: true }
    }
}
