import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    dependencies: ["QtQuick 2.0"]
    Component {
        file: "qquicklinearlayout_p.h"
        name: "QQuickColumnLayout"
        prototype: "QQuickLinearLayout"
        exports: [
            "QtQuick.Layouts/ColumnLayout 1.0",
            "QtQuick.Layouts/ColumnLayout 1.1",
            "QtQuick.Layouts/ColumnLayout 1.11",
            "QtQuick.Layouts/ColumnLayout 1.4",
            "QtQuick.Layouts/ColumnLayout 1.7"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 7]
    }
    Component {
        file: "qquicklinearlayout_p.h"
        name: "QQuickGridLayout"
        prototype: "QQuickGridLayoutBase"
        exports: [
            "QtQuick.Layouts/GridLayout 1.0",
            "QtQuick.Layouts/GridLayout 1.1",
            "QtQuick.Layouts/GridLayout 1.11",
            "QtQuick.Layouts/GridLayout 1.4",
            "QtQuick.Layouts/GridLayout 1.7"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 7]
        Enum {
            name: "Flow"
            values: ["LeftToRight", "TopToBottom"]
        }
        Property { name: "columnSpacing"; type: "double" }
        Property { name: "rowSpacing"; type: "double" }
        Property { name: "columns"; type: "int" }
        Property { name: "rows"; type: "int" }
        Property { name: "flow"; type: "Flow" }
    }
    Component {
        file: "qquicklinearlayout_p.h"
        name: "QQuickGridLayoutBase"
        prototype: "QQuickLayout"
        Property { name: "layoutDirection"; revision: 1; type: "Qt::LayoutDirection" }
        Signal { name: "layoutDirectionChanged"; revision: 1 }
    }
    Component {
        file: "qquicklayout_p.h"
        name: "QQuickLayout"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: [
            "QtQuick.Layouts/Layout 1.0",
            "QtQuick.Layouts/Layout 1.1",
            "QtQuick.Layouts/Layout 1.11",
            "QtQuick.Layouts/Layout 1.4",
            "QtQuick.Layouts/Layout 1.7"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [0, 1, 11, 4, 7]
        attachedType: "QQuickLayoutAttached"
        Method { name: "invalidateSenderItem" }
        Method { name: "_q_dumpLayoutTree" }
    }
    Component {
        name: "QQuickLayoutAttached"
        prototype: "QObject"
        Property { name: "minimumWidth"; type: "double" }
        Property { name: "minimumHeight"; type: "double" }
        Property { name: "preferredWidth"; type: "double" }
        Property { name: "preferredHeight"; type: "double" }
        Property { name: "maximumWidth"; type: "double" }
        Property { name: "maximumHeight"; type: "double" }
        Property { name: "fillHeight"; type: "bool" }
        Property { name: "fillWidth"; type: "bool" }
        Property { name: "row"; type: "int" }
        Property { name: "column"; type: "int" }
        Property { name: "rowSpan"; type: "int" }
        Property { name: "columnSpan"; type: "int" }
        Property { name: "alignment"; type: "Qt::Alignment" }
        Property { name: "margins"; type: "double" }
        Property { name: "leftMargin"; type: "double" }
        Property { name: "topMargin"; type: "double" }
        Property { name: "rightMargin"; type: "double" }
        Property { name: "bottomMargin"; type: "double" }
    }
    Component {
        file: "qquicklinearlayout_p.h"
        name: "QQuickLinearLayout"
        prototype: "QQuickGridLayoutBase"
        Property { name: "spacing"; type: "double" }
    }
    Component {
        file: "qquicklinearlayout_p.h"
        name: "QQuickRowLayout"
        prototype: "QQuickLinearLayout"
        exports: [
            "QtQuick.Layouts/RowLayout 1.0",
            "QtQuick.Layouts/RowLayout 1.1",
            "QtQuick.Layouts/RowLayout 1.11",
            "QtQuick.Layouts/RowLayout 1.4",
            "QtQuick.Layouts/RowLayout 1.7"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 7]
    }
    Component {
        file: "qquickstacklayout_p.h"
        name: "QQuickStackLayout"
        prototype: "QQuickLayout"
        exports: [
            "QtQuick.Layouts/StackLayout 1.11",
            "QtQuick.Layouts/StackLayout 1.3",
            "QtQuick.Layouts/StackLayout 1.4",
            "QtQuick.Layouts/StackLayout 1.7"
        ]
        exportMetaObjectRevisions: [11, 3, 4, 7]
        Property { name: "count"; type: "int"; isReadonly: true }
        Property { name: "currentIndex"; type: "int" }
        Method {
            name: "itemAt"
            type: "QQuickItem*"
            Parameter { name: "index"; type: "int" }
        }
    }
}
