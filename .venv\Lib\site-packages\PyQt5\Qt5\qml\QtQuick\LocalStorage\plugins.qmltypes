import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    dependencies: []
    Component {
        file: "qquicklocalstorage_p.h"
        name: "QQuickLocalStorage"
        prototype: "QObject"
        exports: ["QtQuick.LocalStorage/LocalStorage 2.0"]
        isCreatable: false
        isSingleton: true
        exportMetaObjectRevisions: [0]
        Method {
            name: "openDatabaseSync"
            Parameter { name: "args"; type: "QQmlV4Function"; isPointer: true }
        }
    }
}
