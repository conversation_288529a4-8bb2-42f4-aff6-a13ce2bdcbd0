import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    dependencies: ["QtQuick 2.0", "QtQuick.Window 2.0"]
    Component {
        file: "quicktestevent_p.h"
        name: "QQuickTouchEventSequence"
        prototype: "QObject"
        Method {
            name: "press"
            type: "QObject*"
            Parameter { name: "touchId"; type: "int" }
            Parameter { name: "item"; type: "QObject"; isPointer: true }
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
        Method {
            name: "move"
            type: "QObject*"
            Parameter { name: "touchId"; type: "int" }
            Parameter { name: "item"; type: "QObject"; isPointer: true }
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
        Method {
            name: "release"
            type: "QObject*"
            Parameter { name: "touchId"; type: "int" }
            Parameter { name: "item"; type: "QObject"; isPointer: true }
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
        Method {
            name: "stationary"
            type: "QObject*"
            Parameter { name: "touchId"; type: "int" }
        }
        Method { name: "commit"; type: "QObject*" }
    }
    Component {
        file: "quicktestevent_p.h"
        name: "QuickTestEvent"
        prototype: "QObject"
        exports: ["QtTest/TestEvent 1.0", "QtTest/TestEvent 1.2"]
        exportMetaObjectRevisions: [0, 2]
        Property { name: "defaultMouseDelay"; type: "int"; isReadonly: true }
        Method {
            name: "keyPress"
            type: "bool"
            Parameter { name: "key"; type: "int" }
            Parameter { name: "modifiers"; type: "int" }
            Parameter { name: "delay"; type: "int" }
        }
        Method {
            name: "keyRelease"
            type: "bool"
            Parameter { name: "key"; type: "int" }
            Parameter { name: "modifiers"; type: "int" }
            Parameter { name: "delay"; type: "int" }
        }
        Method {
            name: "keyClick"
            type: "bool"
            Parameter { name: "key"; type: "int" }
            Parameter { name: "modifiers"; type: "int" }
            Parameter { name: "delay"; type: "int" }
        }
        Method {
            name: "keyPressChar"
            type: "bool"
            Parameter { name: "character"; type: "string" }
            Parameter { name: "modifiers"; type: "int" }
            Parameter { name: "delay"; type: "int" }
        }
        Method {
            name: "keyReleaseChar"
            type: "bool"
            Parameter { name: "character"; type: "string" }
            Parameter { name: "modifiers"; type: "int" }
            Parameter { name: "delay"; type: "int" }
        }
        Method {
            name: "keyClickChar"
            type: "bool"
            Parameter { name: "character"; type: "string" }
            Parameter { name: "modifiers"; type: "int" }
            Parameter { name: "delay"; type: "int" }
        }
        Method {
            name: "keySequence"
            revision: 2
            type: "bool"
            Parameter { name: "keySequence"; type: "QVariant" }
        }
        Method {
            name: "mousePress"
            type: "bool"
            Parameter { name: "item"; type: "QObject"; isPointer: true }
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
            Parameter { name: "button"; type: "int" }
            Parameter { name: "modifiers"; type: "int" }
            Parameter { name: "delay"; type: "int" }
        }
        Method {
            name: "mouseRelease"
            type: "bool"
            Parameter { name: "item"; type: "QObject"; isPointer: true }
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
            Parameter { name: "button"; type: "int" }
            Parameter { name: "modifiers"; type: "int" }
            Parameter { name: "delay"; type: "int" }
        }
        Method {
            name: "mouseClick"
            type: "bool"
            Parameter { name: "item"; type: "QObject"; isPointer: true }
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
            Parameter { name: "button"; type: "int" }
            Parameter { name: "modifiers"; type: "int" }
            Parameter { name: "delay"; type: "int" }
        }
        Method {
            name: "mouseDoubleClick"
            type: "bool"
            Parameter { name: "item"; type: "QObject"; isPointer: true }
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
            Parameter { name: "button"; type: "int" }
            Parameter { name: "modifiers"; type: "int" }
            Parameter { name: "delay"; type: "int" }
        }
        Method {
            name: "mouseDoubleClickSequence"
            type: "bool"
            Parameter { name: "item"; type: "QObject"; isPointer: true }
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
            Parameter { name: "button"; type: "int" }
            Parameter { name: "modifiers"; type: "int" }
            Parameter { name: "delay"; type: "int" }
        }
        Method {
            name: "mouseMove"
            type: "bool"
            Parameter { name: "item"; type: "QObject"; isPointer: true }
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
            Parameter { name: "delay"; type: "int" }
            Parameter { name: "buttons"; type: "int" }
        }
        Method {
            name: "mouseWheel"
            type: "bool"
            Parameter { name: "item"; type: "QObject"; isPointer: true }
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
            Parameter { name: "buttons"; type: "int" }
            Parameter { name: "modifiers"; type: "int" }
            Parameter { name: "xDelta"; type: "int" }
            Parameter { name: "yDelta"; type: "int" }
            Parameter { name: "delay"; type: "int" }
        }
        Method {
            name: "touchEvent"
            type: "QQuickTouchEventSequence*"
            Parameter { name: "item"; type: "QObject"; isPointer: true }
        }
        Method { name: "touchEvent"; type: "QQuickTouchEventSequence*" }
    }
    Component {
        file: "quicktestresultforeign_p.h"
        name: "QuickTestResult"
        prototype: "QObject"
        exports: [
            "QtTest/TestResult 1.0",
            "QtTest/TestResult 1.1",
            "QtTest/TestResult 1.13"
        ]
        exportMetaObjectRevisions: [0, 1, 13]
        Enum {
            name: "RunMode"
            values: ["RepeatUntilValidMeasurement", "RunOnce"]
        }
        Property { name: "testCaseName"; type: "string" }
        Property { name: "functionName"; type: "string" }
        Property { name: "dataTag"; type: "string" }
        Property { name: "failed"; type: "bool"; isReadonly: true }
        Property { name: "skipped"; type: "bool" }
        Property { name: "passCount"; type: "int"; isReadonly: true }
        Property { name: "failCount"; type: "int"; isReadonly: true }
        Property { name: "skipCount"; type: "int"; isReadonly: true }
        Property { name: "functionsToRun"; type: "QStringList"; isReadonly: true }
        Property { name: "tagsToRun"; type: "QStringList"; isReadonly: true }
        Signal { name: "programNameChanged" }
        Method { name: "reset" }
        Method { name: "startLogging" }
        Method { name: "stopLogging" }
        Method { name: "initTestTable" }
        Method { name: "clearTestTable" }
        Method { name: "finishTestData" }
        Method { name: "finishTestDataCleanup" }
        Method { name: "finishTestFunction" }
        Method {
            name: "stringify"
            Parameter { name: "args"; type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "fail"
            Parameter { name: "message"; type: "string" }
            Parameter { name: "location"; type: "QUrl" }
            Parameter { name: "line"; type: "int" }
        }
        Method {
            name: "verify"
            type: "bool"
            Parameter { name: "success"; type: "bool" }
            Parameter { name: "message"; type: "string" }
            Parameter { name: "location"; type: "QUrl" }
            Parameter { name: "line"; type: "int" }
        }
        Method {
            name: "compare"
            type: "bool"
            Parameter { name: "success"; type: "bool" }
            Parameter { name: "message"; type: "string" }
            Parameter { name: "val1"; type: "QVariant" }
            Parameter { name: "val2"; type: "QVariant" }
            Parameter { name: "location"; type: "QUrl" }
            Parameter { name: "line"; type: "int" }
        }
        Method {
            name: "fuzzyCompare"
            type: "bool"
            Parameter { name: "actual"; type: "QVariant" }
            Parameter { name: "expected"; type: "QVariant" }
            Parameter { name: "delta"; type: "double" }
        }
        Method {
            name: "skip"
            Parameter { name: "message"; type: "string" }
            Parameter { name: "location"; type: "QUrl" }
            Parameter { name: "line"; type: "int" }
        }
        Method {
            name: "expectFail"
            type: "bool"
            Parameter { name: "tag"; type: "string" }
            Parameter { name: "comment"; type: "string" }
            Parameter { name: "location"; type: "QUrl" }
            Parameter { name: "line"; type: "int" }
        }
        Method {
            name: "expectFailContinue"
            type: "bool"
            Parameter { name: "tag"; type: "string" }
            Parameter { name: "comment"; type: "string" }
            Parameter { name: "location"; type: "QUrl" }
            Parameter { name: "line"; type: "int" }
        }
        Method {
            name: "warn"
            Parameter { name: "message"; type: "string" }
            Parameter { name: "location"; type: "QUrl" }
            Parameter { name: "line"; type: "int" }
        }
        Method {
            name: "ignoreWarning"
            Parameter { name: "message"; type: "QJSValue" }
        }
        Method {
            name: "wait"
            Parameter { name: "ms"; type: "int" }
        }
        Method {
            name: "sleep"
            Parameter { name: "ms"; type: "int" }
        }
        Method {
            name: "waitForRendering"
            type: "bool"
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
            Parameter { name: "timeout"; type: "int" }
        }
        Method {
            name: "waitForRendering"
            type: "bool"
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
        }
        Method { name: "startMeasurement" }
        Method { name: "beginDataRun" }
        Method { name: "endDataRun" }
        Method { name: "measurementAccepted"; type: "bool" }
        Method { name: "needsMoreMeasurements"; type: "bool" }
        Method {
            name: "startBenchmark"
            Parameter { name: "runMode"; type: "RunMode" }
            Parameter { name: "tag"; type: "string" }
        }
        Method { name: "isBenchmarkDone"; type: "bool" }
        Method { name: "nextBenchmark" }
        Method { name: "stopBenchmark" }
        Method {
            name: "grabImage"
            type: "QObject*"
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "findChild"
            revision: 1
            type: "QObject*"
            Parameter { name: "parent"; type: "QObject"; isPointer: true }
            Parameter { name: "objectName"; type: "string" }
        }
        Method {
            name: "isPolishScheduled"
            revision: 13
            type: "bool"
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "waitForItemPolished"
            revision: 13
            type: "bool"
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
            Parameter { name: "timeout"; type: "int" }
        }
    }
    Component {
        file: "quicktestutil_p.h"
        name: "QuickTestUtil"
        prototype: "QObject"
        exports: ["QtTest/TestUtil 1.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "printAvailableFunctions"; type: "bool"; isReadonly: true }
        Property { name: "dragThreshold"; type: "int"; isReadonly: true }
        Method {
            name: "typeName"
            type: "QJSValue"
            Parameter { name: "v"; type: "QVariant" }
        }
        Method {
            name: "compare"
            type: "bool"
            Parameter { name: "act"; type: "QVariant" }
            Parameter { name: "exp"; type: "QVariant" }
        }
        Method {
            name: "callerFile"
            type: "QJSValue"
            Parameter { name: "frameIndex"; type: "int" }
        }
        Method { name: "callerFile"; type: "QJSValue" }
        Method {
            name: "callerLine"
            type: "int"
            Parameter { name: "frameIndex"; type: "int" }
        }
        Method { name: "callerLine"; type: "int" }
    }
}
