import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by:
// 'qmlplugindump -nonrelocatable -dependencies dependencies.json QtWebChannel 1.15'

Module {
    dependencies: []
    Component {
        name: "QQmlWebChannel"
        prototype: "QWebChannel"
        exports: ["QtWebChannel/WebChannel 1.0"]
        exportMetaObjectRevisions: [0]
        attachedType: "QQmlWebChannelAttached"
        Property { name: "transports"; type: "QObject"; isList: true; isReadonly: true }
        Property { name: "registeredObjects"; type: "QObject"; isList: true; isReadonly: true }
        Method {
            name: "registerObjects"
            Parameter { name: "objects"; type: "QVariantMap" }
        }
        Method {
            name: "connectTo"
            Parameter { name: "transport"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "disconnectFrom"
            Parameter { name: "transport"; type: "QObject"; isPointer: true }
        }
    }
    Component {
        name: "QQmlWebChannelAttached"
        prototype: "QObject"
        Property { name: "id"; type: "string" }
        Signal {
            name: "idChanged"
            Parameter { name: "id"; type: "string" }
        }
    }
    Component {
        name: "QWebChannel"
        prototype: "QObject"
        Property { name: "blockUpdates"; type: "bool" }
        Signal {
            name: "blockUpdatesChanged"
            Parameter { name: "block"; type: "bool" }
        }
        Method {
            name: "connectTo"
            Parameter { name: "transport"; type: "QWebChannelAbstractTransport"; isPointer: true }
        }
        Method {
            name: "disconnectFrom"
            Parameter { name: "transport"; type: "QWebChannelAbstractTransport"; isPointer: true }
        }
        Method {
            name: "registerObject"
            Parameter { name: "id"; type: "string" }
            Parameter { name: "object"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "deregisterObject"
            Parameter { name: "object"; type: "QObject"; isPointer: true }
        }
    }
}
