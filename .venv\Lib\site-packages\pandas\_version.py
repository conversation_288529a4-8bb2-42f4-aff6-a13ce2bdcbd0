
# This file was generated by 'versioneer.py' (0.28) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2023-04-03T14:48:33+0200",
 "dirty": false,
 "error": null,
 "full-revisionid": "478d340667831908b5b4bf09a2787a11a14560c9",
 "version": "2.0.0"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
