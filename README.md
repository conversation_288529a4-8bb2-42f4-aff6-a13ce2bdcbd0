# Kotak Neo Trading Bot

A professional real-time options trading application for the Indian stock market (NSE F&O) using the Kotak Neo API.

## Features

- **Real-time Price Monitoring**: WebSocket-based tick-by-tick price data with minimal latency
- **Automated Trading**: Scheduled buy orders with automatic stop-loss and profit target execution
- **Portfolio Management**: Real-time position tracking and P&L calculation
- **Professional UI**: Dark-themed Windows desktop interface optimized for trading
- **Risk Management**: Built-in stop-loss and profit target mechanisms
- **Trade History**: Comprehensive trade logging and export functionality

## Requirements

- Windows 10/11
- Python 3.7 or higher
- Valid Kotak Neo API credentials
- Active internet connection

## Installation

1. **Download and Extract**
   - Download the application files to a folder (e.g., `C:\TradingBot`)

2. **Run Setup**
   ```bash
   python setup.py
   ```
   This will:
   - Install required Python packages
   - Create necessary directories
   - Setup initial configuration
   - Create desktop shortcut (optional)

3. **Configure API Credentials**
   - Edit `user_config.json` and add your Kotak Neo API credentials:
   ```json
   {
     "neo_fin_key": "your_neo_fin_key_here",
     "mobile_number": "your_registered_mobile_number"
   }
   ```

## Usage

### Starting the Application

Run the application using:
```bash
python main.py
```

Or use the desktop shortcut if created during setup.

### First Time Login

1. The application will prompt for your mobile number
2. An OTP will be sent to your registered mobile
3. Enter the OTP to complete authentication
4. The session will be saved for future use

### Adding Symbols to Watchlist

1. Go to the "Symbols & Watchlist" tab
2. Select instrument (NIFTY/BANKNIFTY)
3. Enter expiry date (DD-MMM-YYYY format)
4. Set strike price
5. Choose option type (PE/CE)
6. Click "Add Symbol"

### Setting Up Automated Trading

1. Go to the "Trading Controls" tab
2. Configure trading parameters:
   - **Quantity**: Number of lots to trade
   - **Buy Time**: Execution time (HH:MM:SS format, default: 09:19:58)
   - **Stop Loss**: Loss amount in ₹ (default: ₹1)
   - **Profit Target**: Profit amount in ₹ (default: ₹1)
3. Enable "Auto Trading" checkbox
4. Click "Update Parameters"

### Monitoring Positions and Trades

- **Positions Tab**: View current positions with real-time P&L
- **Trades Tab**: View today's trade history
- **Log Panel**: Monitor application status and errors

## Configuration

### Trading Parameters

Edit `user_config.json` to customize:

```json
{
  "trading_preferences": {
    "default_quantity": 50,
    "default_buy_time": "09:19:58",
    "stop_loss_amount": 1.0,
    "profit_target_amount": 1.0,
    "auto_trading_enabled": false
  }
}
```

### UI Preferences

```json
{
  "ui_preferences": {
    "window_width": 1200,
    "window_height": 800,
    "dark_theme": true,
    "update_interval": 5000
  }
}
```

## How It Works

### Automated Trading Flow

1. **Scheduled Entry**: At the specified time (default 9:19:58 AM), a market buy order is placed
2. **Position Monitoring**: Once the order is executed, the system monitors real-time price changes
3. **Automatic Exit**: When price moves by the specified amount (₹1 by default), a market sell order is triggered
4. **Trade Completion**: The system calculates P&L and logs the completed trade

### Real-time Data

- Uses WebSocket connection for tick-by-tick price updates
- Automatic reconnection with exponential backoff
- Immediate trigger on price changes (no polling delays)

### Risk Management

- Stop-loss triggers when price drops by specified amount from buy price
- Profit target triggers when price rises by specified amount from buy price
- Only one exit order per position (first trigger wins)
- Market orders ensure immediate execution

## API Endpoints Used

- **Authentication**: `/login/v3/customer/otp`, `/login/v3/customer/login`
- **Instruments**: `/v3/scrips/master`
- **Orders**: `/v3/orders`
- **Positions**: `/v3/portfolio/positions`
- **Trades**: `/v3/orders/trades`
- **WebSocket**: Real-time market data feed

## Troubleshooting

### Common Issues

1. **Login Failed**
   - Verify mobile number is registered with Kotak Neo
   - Check internet connection
   - Ensure OTP is entered correctly

2. **WebSocket Connection Failed**
   - Check firewall settings
   - Verify API credentials
   - Restart application

3. **Symbol Not Found**
   - Refresh instrument master data (Tools → Refresh Instruments)
   - Verify expiry date format (DD-MMM-YYYY)
   - Check if symbol exists in NSE F&O

4. **Orders Not Executing**
   - Verify sufficient margin in account
   - Check market hours
   - Ensure auto trading is enabled

### Log Files

- Application logs are saved to `trading_bot.log`
- Check logs for detailed error information
- Logs rotate automatically (max 10MB per file)

## Important Notes

### Market Hours
- The application works during NSE trading hours (9:15 AM - 3:30 PM IST)
- Pre-market and after-market sessions may have limited functionality

### Risk Disclaimer
- This is a trading tool that can place real orders
- Always test with small quantities first
- Monitor positions actively during market hours
- The developers are not responsible for trading losses

### Session Management
- Login sessions are valid for 24 hours
- The application will prompt for re-login when session expires
- Session data is stored securely on local machine

## Support

For issues and questions:
1. Check the log files for error details
2. Verify API credentials and connection
3. Ensure all dependencies are installed correctly

## License

This software is provided as-is for educational and trading purposes. Use at your own risk.
