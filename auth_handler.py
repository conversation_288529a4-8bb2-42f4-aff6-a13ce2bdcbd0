# auth_handler.py - Authentication handler for Kotak Neo API

import requests
import json
import os
from datetime import datetime, timedelta
import config
from logger import logger

class AuthHandler:
    """Handles authentication with Kotak Neo API"""
    
    def __init__(self):
        self.sid = None
        self.auth_token = None
        self.session_expiry = None
        self.mobile_number = None
        self.load_session()
    
    def load_session(self):
        """Load existing session from file if valid"""
        try:
            if os.path.exists(config.SESSION_FILE):
                with open(config.SESSION_FILE, 'r') as f:
                    session_data = json.load(f)
                
                # Check if session is still valid
                expiry_str = session_data.get('expiry')
                if expiry_str:
                    expiry = datetime.fromisoformat(expiry_str)
                    if datetime.now() < expiry:
                        self.sid = session_data.get('sid')
                        self.auth_token = session_data.get('auth_token')
                        self.session_expiry = expiry
                        self.mobile_number = session_data.get('mobile_number')
                        self.update_headers()
                        logger.info("Loaded existing valid session")
                        return True
                
                # Session expired, remove file
                os.remove(config.SESSION_FILE)
                logger.info("Existing session expired, removed session file")
        except Exception as e:
            logger.error(f"Error loading session: {e}")
        
        return False
    
    def save_session(self):
        """Save current session to file"""
        try:
            session_data = {
                'sid': self.sid,
                'auth_token': self.auth_token,
                'expiry': self.session_expiry.isoformat() if self.session_expiry else None,
                'mobile_number': self.mobile_number
            }
            with open(config.SESSION_FILE, 'w') as f:
                json.dump(session_data, f, indent=2)
            logger.info("Session saved successfully")
        except Exception as e:
            logger.error(f"Error saving session: {e}")
    
    def update_headers(self):
        """Update global API headers with session tokens"""
        if self.sid and self.auth_token:
            config.API_HEADERS['Authorization'] = f"Bearer {self.sid}"
            config.API_HEADERS['Sid'] = self.sid
            config.API_HEADERS['Auth'] = self.auth_token
    
    def request_otp(self, mobile_number):
        """Request OTP for the given mobile number"""
        self.mobile_number = mobile_number
        url = f"{config.BASE_URL}/login/v3/customer/otp"
        payload = {"mobileNumber": mobile_number}
        
        try:
            logger.info(f"Requesting OTP for mobile: {mobile_number}")
            response = requests.post(url, headers=config.API_HEADERS, json=payload, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            if result.get('status') == 'success':
                logger.info("OTP sent successfully")
                return True, "OTP sent successfully"
            else:
                error_msg = result.get('message', 'Unknown error')
                logger.error(f"OTP request failed: {error_msg}")
                return False, error_msg
                
        except requests.exceptions.Timeout:
            error_msg = "Request timeout - please check your internet connection"
            logger.error(error_msg)
            return False, error_msg
        except requests.exceptions.RequestException as e:
            error_msg = f"Network error: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
        except Exception as e:
            error_msg = f"Unexpected error: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def validate_otp(self, otp):
        """Validate OTP and generate session"""
        if not self.mobile_number:
            return False, "Mobile number not set"
        
        url = f"{config.BASE_URL}/login/v3/customer/login"
        payload = {
            "mobileNumber": self.mobile_number,
            "password": otp
        }
        
        try:
            logger.info("Validating OTP and generating session")
            response = requests.post(url, headers=config.API_HEADERS, json=payload, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            if result.get('status') == 'success':
                data = result.get('data', {})
                self.sid = data.get('sid')
                self.auth_token = data.get('auth')
                
                if self.sid and self.auth_token:
                    # Set session expiry (typically 24 hours)
                    self.session_expiry = datetime.now() + timedelta(hours=23)
                    self.update_headers()
                    self.save_session()
                    logger.info("Session generated successfully")
                    return True, "Login successful"
                else:
                    error_msg = "Failed to retrieve session tokens"
                    logger.error(error_msg)
                    return False, error_msg
            else:
                error_msg = result.get('message', 'Invalid OTP')
                logger.error(f"OTP validation failed: {error_msg}")
                return False, error_msg
                
        except requests.exceptions.Timeout:
            error_msg = "Request timeout - please check your internet connection"
            logger.error(error_msg)
            return False, error_msg
        except requests.exceptions.RequestException as e:
            error_msg = f"Network error: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
        except Exception as e:
            error_msg = f"Unexpected error: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def is_authenticated(self):
        """Check if user is currently authenticated"""
        if not self.sid or not self.auth_token:
            return False
        
        if self.session_expiry and datetime.now() >= self.session_expiry:
            logger.info("Session expired")
            self.logout()
            return False
        
        return True
    
    def logout(self):
        """Clear session data"""
        self.sid = None
        self.auth_token = None
        self.session_expiry = None
        self.mobile_number = None
        
        # Clear headers
        config.API_HEADERS.pop('Authorization', None)
        config.API_HEADERS.pop('Sid', None)
        config.API_HEADERS.pop('Auth', None)
        
        # Remove session file
        try:
            if os.path.exists(config.SESSION_FILE):
                os.remove(config.SESSION_FILE)
        except Exception as e:
            logger.error(f"Error removing session file: {e}")
        
        logger.info("Logged out successfully")
    
    def verify_connection(self):
        """Verify API connection by making a test call"""
        if not self.is_authenticated():
            return False, "Not authenticated"
        
        try:
            # Test with positions endpoint
            url = f"{config.BASE_URL}/v3/portfolio/positions"
            response = requests.get(url, headers=config.API_HEADERS, timeout=10)
            
            if response.status_code == 200:
                logger.info("API connection verified successfully")
                return True, "Connection verified"
            elif response.status_code == 401:
                logger.error("Authentication failed - session may be invalid")
                self.logout()
                return False, "Authentication failed"
            else:
                error_msg = f"API error: {response.status_code}"
                logger.error(error_msg)
                return False, error_msg
                
        except requests.exceptions.Timeout:
            error_msg = "Connection timeout"
            logger.error(error_msg)
            return False, error_msg
        except requests.exceptions.RequestException as e:
            error_msg = f"Connection error: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
        except Exception as e:
            error_msg = f"Unexpected error: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
