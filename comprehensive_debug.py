#!/usr/bin/env python3
"""
Comprehensive Debug Session - Single OTP Login
This will test and fix ALL issues in one session
"""

import sys
import os
import pandas as pd
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.api.kotak_client import KotakClient
from src.utils.config_manager import ConfigManager
from src.utils.logger import get_logger

logger = get_logger()

def otp_callback(message):
    """Callback function to get OTP from user"""
    print(f"\n🔐 {message}")
    otp = input("Enter OTP: ").strip()
    return otp

def comprehensive_debug():
    """Complete debugging session with single OTP"""
    print("🔧 COMPREHENSIVE DEBUG SESSION")
    print("=" * 60)
    
    try:
        # Initialize and login ONCE
        config_manager = ConfigManager()
        config = config_manager._config
        client = KotakClient(config)
        
        print("🔑 SINGLE OTP LOGIN...")
        success = client.login(otp_callback=otp_callback)
        
        if not success or not client.is_authenticated:
            print("❌ Login failed - cannot proceed")
            return False
        
        print("✅ Login successful - proceeding with comprehensive testing")
        print(f"   User: {client.user_name} | ID: {client.user_id}")
        
        # TEST 1: Scrip Master Analysis
        print("\n" + "="*60)
        print("1️⃣ SCRIP MASTER DETAILED ANALYSIS")
        print("="*60)
        
        scrip_df = client.fetch_scrip_master_data()
        if scrip_df is not None:
            print(f"✅ Scrip master loaded: {len(scrip_df)} instruments")
            
            # Analyze NIFTY options specifically
            nifty_options = scrip_df[scrip_df['pSymbolName'] == 'NIFTY']
            print(f"   NIFTY instruments found: {len(nifty_options)}")
            
            if len(nifty_options) > 0:
                print(f"\n📊 NIFTY OPTIONS ANALYSIS:")
                
                # Check expiry dates
                expiry_dates = nifty_options['pExpiryDate'].dt.date.unique()
                print(f"   Available expiry dates: {sorted(expiry_dates)[:10]}...")
                
                # Check if July 10, 2025 exists
                target_date = pd.to_datetime('2025-07-10').date()
                july_10_options = nifty_options[nifty_options['pExpiryDate'].dt.date == target_date]
                print(f"   July 10, 2025 options: {len(july_10_options)}")
                
                if len(july_10_options) > 0:
                    strikes = july_10_options['dStrikePrice;'].unique()
                    print(f"   Available strikes for July 10: {sorted(strikes)[:20]}...")
                    
                    # Check if 25500 strike exists
                    if 25500.0 in strikes:
                        print(f"   ✅ Strike 25500 available for July 10, 2025")
                        
                        # Check PE options
                        pe_options = july_10_options[
                            (july_10_options['dStrikePrice;'] == 25500.0) & 
                            (july_10_options['pOptionType'] == 'PE')
                        ]
                        print(f"   PE options for 25500 strike: {len(pe_options)}")
                        
                        if len(pe_options) > 0:
                            sample = pe_options.iloc[0]
                            print(f"   ✅ FOUND TARGET OPTION:")
                            print(f"      Trading Symbol: {sample['pTrdSymbol']}")
                            print(f"      Instrument Token: {sample['pSymbol']}")
                            print(f"      Lot Size: {sample['lLotSize']}")
                            print(f"      Strike: {sample['dStrikePrice;']}")
                        else:
                            print(f"   ❌ No PE options found for 25500 strike")
                    else:
                        print(f"   ❌ Strike 25500 not available for July 10, 2025")
                        print(f"   Available strikes: {sorted(strikes)}")
                else:
                    print(f"   ❌ No options found for July 10, 2025")
                    print(f"   Try these dates instead: {sorted(expiry_dates)[:5]}")
            else:
                print(f"   ❌ No NIFTY instruments found")
        else:
            print("❌ Failed to load scrip master")
            return False
        
        # TEST 2: Symbol Search Testing
        print("\n" + "="*60)
        print("2️⃣ SYMBOL SEARCH TESTING")
        print("="*60)
        
        test_cases = [
            ('NIFTY', '10JUL25', '25500', 'PE', 'Your original request'),
            ('NIFTY', '10JUL25', '24000', 'PE', 'Alternative strike'),
            ('NIFTY', '17JUL25', '25500', 'PE', 'Alternative date'),
            ('BANKNIFTY', '10JUL25', '50000', 'PE', 'BANKNIFTY test'),
        ]
        
        for base, date, strike, opt_type, desc in test_cases:
            print(f"\n🔍 Testing: {desc}")
            print(f"   Parameters: {base} {date} {strike} {opt_type}")
            
            try:
                # Test direct search
                result = client.search_symbol_direct(base, date, strike, opt_type)
                if result:
                    print(f"   ✅ Direct search found: {result}")
                else:
                    print(f"   ❌ Direct search failed")
                
                # Test instrument search
                formatted_date = f"2025-07-{date[:2]}"
                instrument = client.find_option_instrument(base, formatted_date, strike, opt_type)
                if instrument:
                    print(f"   ✅ Instrument search found: {instrument['trading_symbol']}")
                    print(f"      Lot size: {instrument['lot_size']}")
                else:
                    print(f"   ❌ Instrument search failed")
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        # TEST 3: Order Placement Testing (DRY RUN)
        print("\n" + "="*60)
        print("3️⃣ ORDER PLACEMENT TESTING (DRY RUN)")
        print("="*60)
        
        # Find a valid symbol first
        valid_symbol = None
        valid_lot_size = 75
        
        if len(nifty_options) > 0:
            # Use the first available NIFTY option
            sample_option = nifty_options.iloc[0]
            valid_symbol = sample_option['pTrdSymbol']
            valid_lot_size = sample_option['lLotSize']
            
            print(f"📊 Testing with valid symbol: {valid_symbol}")
            print(f"   Lot size: {valid_lot_size}")
            
            # Test quantity calculation
            lots = 1
            shares = lots * valid_lot_size
            print(f"   Quantity conversion: {lots} lot × {valid_lot_size} = {shares} shares")
            
            # Test order parameters (without actually placing order)
            print(f"   Order parameters would be:")
            print(f"      Symbol: {valid_symbol}")
            print(f"      Quantity: {shares}")
            print(f"      Order Type: MKT")
            print(f"      Transaction: BUY")
            print(f"   ✅ Order parameters prepared correctly")
        
        # TEST 4: GUI Integration Testing
        print("\n" + "="*60)
        print("4️⃣ GUI INTEGRATION TESTING")
        print("="*60)
        
        # Test symbol building format
        test_symbol = "NIFTY|10JUL25|25500|PE"
        print(f"📊 Testing GUI symbol format: {test_symbol}")
        
        try:
            # This tests the symbol parsing logic
            parts = test_symbol.split('|')
            if len(parts) == 4:
                base, date, strike, opt_type = parts
                print(f"   ✅ Symbol parsing successful:")
                print(f"      Base: {base}")
                print(f"      Date: {date}")
                print(f"      Strike: {strike}")
                print(f"      Type: {opt_type}")
                
                # Test date conversion
                if len(date) == 7:
                    day = date[:2]
                    month = date[2:5]
                    year = f"20{date[5:7]}"
                    month_map = {'JAN':'01','FEB':'02','MAR':'03','APR':'04','MAY':'05','JUN':'06','JUL':'07','AUG':'08','SEP':'09','OCT':'10','NOV':'11','DEC':'12'}
                    formatted = f"{year}-{month_map[month]}-{day}"
                    print(f"   ✅ Date conversion: {date} → {formatted}")
                else:
                    print(f"   ❌ Invalid date format: {date}")
            else:
                print(f"   ❌ Invalid symbol format")
        except Exception as e:
            print(f"   ❌ Symbol parsing error: {e}")
        
        print("\n" + "="*60)
        print("🎉 COMPREHENSIVE DEBUG COMPLETED")
        print("="*60)
        
        return True
        
    except Exception as e:
        print(f"❌ CRITICAL ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 COMPREHENSIVE DEBUG SESSION")
    print("This will test ALL functionality with SINGLE OTP")
    print("Please have your phone ready for OTP")
    
    input("\nPress Enter to start comprehensive debugging...")
    
    success = comprehensive_debug()
    
    if success:
        print("\n🎉 ALL DEBUGGING COMPLETED!")
        print("Check the detailed output above for any remaining issues")
    else:
        print("\n❌ DEBUGGING FAILED!")
        print("Critical issues need to be resolved")
    
    print("\n" + "=" * 60)
    print("DEBUG SESSION FINISHED")
