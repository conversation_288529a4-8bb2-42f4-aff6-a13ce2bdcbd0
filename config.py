# config.py - Configuration file for Kotak Neo Trading Bot

import os
import json
from datetime import datetime

# API Configuration
BASE_URL = "https://kapi.kotaksecurities.com/api"
WEBSOCKET_URL = "wss://wskapi.kotaksecurities.com/feed"

# Default API Headers (will be updated with session tokens)
API_HEADERS = {
    'accept': 'application/json',
    'Content-Type': 'application/json',
    'neo-fin-key': ''  # To be filled from config file
}

# Trading Configuration
DEFAULT_SYMBOL = {
    "instrument": "NIFTY",
    "expiry": "10-JUL-2025", 
    "strike": 25500,
    "type": "PE"
}

# Trading Parameters
ORDER_QUANTITY = 50  # Default NIFTY lot size
DEFAULT_BUY_TIME = "09:19:58"  # Default buy execution time
STOP_LOSS_AMOUNT = 1.0  # ₹1 stop loss
PROFIT_TARGET_AMOUNT = 1.0  # ₹1 profit target

# UI Configuration
WINDOW_WIDTH = 1200
WINDOW_HEIGHT = 800
DARK_THEME = True

# Logging Configuration
LOG_LEVEL = "INFO"
LOG_FILE = "trading_bot.log"
MAX_LOG_SIZE = 10 * 1024 * 1024  # 10MB

# File Paths
CONFIG_FILE = "user_config.json"
INSTRUMENTS_FILE = "instruments.csv"
SESSION_FILE = "session.json"

class Config:
    """Configuration manager for the trading application"""
    
    def __init__(self):
        self.user_config = {}
        self.load_config()
    
    def load_config(self):
        """Load user configuration from file"""
        try:
            if os.path.exists(CONFIG_FILE):
                with open(CONFIG_FILE, 'r') as f:
                    self.user_config = json.load(f)
                    # Update API headers with neo-fin-key
                    if 'neo_fin_key' in self.user_config:
                        API_HEADERS['neo-fin-key'] = self.user_config['neo_fin_key']
        except Exception as e:
            print(f"Error loading config: {e}")
            self.user_config = {}
    
    def save_config(self):
        """Save user configuration to file"""
        try:
            with open(CONFIG_FILE, 'w') as f:
                json.dump(self.user_config, f, indent=2)
        except Exception as e:
            print(f"Error saving config: {e}")
    
    def get(self, key, default=None):
        """Get configuration value"""
        return self.user_config.get(key, default)
    
    def set(self, key, value):
        """Set configuration value"""
        self.user_config[key] = value
        self.save_config()

# Global config instance
config = Config()
