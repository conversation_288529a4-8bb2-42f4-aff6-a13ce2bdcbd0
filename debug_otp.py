#!/usr/bin/env python3
# debug_otp.py - Debug script to test OTP request

import requests
import json

# Test configuration
BASE_URL = "https://kapi.kotaksecurities.com/api"
MOBILE_NUMBER = "+919999977894"

# Test with current neo_fin_key
NEO_FIN_KEY = "neotradeapi"

def test_otp_request():
    """Test OTP request with current configuration"""
    print("=" * 50)
    print("Testing Kotak Neo OTP Request")
    print("=" * 50)
    
    url = f"{BASE_URL}/login/v3/customer/otp"
    
    headers = {
        'accept': 'application/json',
        'Content-Type': 'application/json',
        'neo-fin-key': NEO_FIN_KEY
    }
    
    payload = {"mobileNumber": MOBILE_NUMBER}
    
    print(f"URL: {url}")
    print(f"Headers: {json.dumps(headers, indent=2)}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    print("-" * 50)
    
    try:
        print("Sending OTP request...")
        response = requests.post(url, headers=headers, json=payload, timeout=10)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        try:
            response_json = response.json()
            print(f"Response JSON: {json.dumps(response_json, indent=2)}")
        except:
            print(f"Response Text: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('status') == 'success':
                print("✅ OTP request successful!")
                return True
            else:
                print(f"❌ OTP request failed: {result.get('message', 'Unknown error')}")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timeout")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Network error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_with_consumer_credentials():
    """Test with consumer key/secret in headers"""
    print("\n" + "=" * 50)
    print("Testing with Consumer Key/Secret")
    print("=" * 50)
    
    url = f"{BASE_URL}/login/v3/customer/otp"
    
    headers = {
        'accept': 'application/json',
        'Content-Type': 'application/json',
        'neo-fin-key': NEO_FIN_KEY,
        'consumer-key': 'GfS95Qwtx6Pic9Up3P6sqmyRlu8a',
        'consumer-secret': 'v6Foh1P4NjdL6StvUTqMtqBBBdEa'
    }
    
    payload = {"mobileNumber": MOBILE_NUMBER}
    
    print(f"URL: {url}")
    print(f"Headers: {json.dumps(headers, indent=2)}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    print("-" * 50)
    
    try:
        print("Sending OTP request with consumer credentials...")
        response = requests.post(url, headers=headers, json=payload, timeout=10)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        try:
            response_json = response.json()
            print(f"Response JSON: {json.dumps(response_json, indent=2)}")
        except:
            print(f"Response Text: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('status') == 'success':
                print("✅ OTP request successful!")
                return True
            else:
                print(f"❌ OTP request failed: {result.get('message', 'Unknown error')}")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_alternative_endpoint():
    """Test alternative OTP endpoint"""
    print("\n" + "=" * 50)
    print("Testing Alternative Endpoint")
    print("=" * 50)
    
    # Try different endpoint variations
    endpoints = [
        f"{BASE_URL}/login/v2/customer/otp",
        f"{BASE_URL}/login/customer/otp",
        f"{BASE_URL}/v3/login/customer/otp"
    ]
    
    headers = {
        'accept': 'application/json',
        'Content-Type': 'application/json',
        'neo-fin-key': NEO_FIN_KEY
    }
    
    payload = {"mobileNumber": MOBILE_NUMBER}
    
    for endpoint in endpoints:
        print(f"\nTrying endpoint: {endpoint}")
        try:
            response = requests.post(endpoint, headers=headers, json=payload, timeout=10)
            print(f"Status: {response.status_code}")
            if response.status_code != 404:
                try:
                    print(f"Response: {response.json()}")
                except:
                    print(f"Response: {response.text}")
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    print("🔍 Debugging Kotak Neo OTP Request Issue")
    print(f"Mobile Number: {MOBILE_NUMBER}")
    print(f"Neo Fin Key: {NEO_FIN_KEY}")
    
    # Test 1: Basic OTP request
    success1 = test_otp_request()
    
    # Test 2: With consumer credentials
    success2 = test_with_consumer_credentials()
    
    # Test 3: Alternative endpoints
    test_alternative_endpoint()
    
    print("\n" + "=" * 50)
    print("SUMMARY")
    print("=" * 50)
    print(f"Basic OTP Request: {'✅ Success' if success1 else '❌ Failed'}")
    print(f"With Consumer Creds: {'✅ Success' if success2 else '❌ Failed'}")
    
    if not success1 and not success2:
        print("\n🚨 LIKELY ISSUES:")
        print("1. Neo Fin Key 'neotradeapi' is a placeholder - you need the real key")
        print("2. Mobile number might not be registered with Kotak Neo")
        print("3. API endpoint or authentication method might be different")
        print("4. Account might not have API access enabled")
        
        print("\n💡 NEXT STEPS:")
        print("1. Get your real Neo Fin Key from Kotak Neo API portal")
        print("2. Verify mobile number is registered with your trading account")
        print("3. Check if API access is enabled for your account")
