# Real-Time Options Trading Bot for Kotak Neo - Developer Guide

This document provides a comprehensive technical guide for building the Windows desktop trading application as specified. It includes API endpoint details, code snippets in Python, and architectural best practices.

Developer Guide: Real-Time Options Trading Bot for Kotak Neo

This document provides a detailed technical guide for building the Windows desktop trading application as specified. It includes API endpoint details, code snippets in Python, and architectural best practices.

🚀 Phase 0: Project Setup

Before writing any code, set up your Python environment.

1. Install Necessary Libraries:

Generated bash
pip install requests
pip install websocket-client
pip install apscheduler
# For UI, choose one, e.g., PyQt5
pip install PyQt5


2. Core Configuration:
Create a config.py file to store your credentials and constants. Never hardcode credentials directly in the main script.

Generated python
# config.py

# User-specific credentials (to be filled in by the user)
MOBILE_NUMBER = "YOUR_MOBILE_NUMBER"
PASSWORD = "YOUR_API_PASSWORD" # If required by the login flow

# API Base URL
BASE_URL = "https://kapi.kotaksecurities.com/api"

# API Headers (will be updated dynamically with session tokens)
API_HEADERS = {
    'accept': 'application/json',
    'Content-Type': 'application/json',
    'neo-fin-key': 'YOUR_NEO_FIN_KEY' # Get this from Kotak
}

# Default trading parameters
DEFAULT_SYMBOL = {
    "instrument": "NIFTY",
    "expiry": "10-JUL-2025",
    "strike": 25500,
    "type": "PE"
}
ORDER_QUANTITY = 50 # Default NIFTY lot size
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END
🔐 Phase 1: Authentication & Session Management

Objective: Securely log into the Kotak Neo API using OTP and manage the session.

API Endpoints:

POST /login/v3/customer/otp

POST /login/v3/customer/login

Strategy:

Request an OTP for the user's mobile number.

The user enters the OTP in the UI.

Submit the OTP to get a session sid and auth token.

Store these tokens and add them to the headers for all subsequent API calls.

Code (auth_handler.py):

Generated python
import requests
import config

class AuthHandler:
    def __init__(self):
        self.sid = None
        self.auth_token = None

    def request_otp(self, mobile_number):
        """Sends an OTP to the user's mobile number."""
        url = f"{config.BASE_URL}/login/v3/customer/otp"
        payload = {"mobileNumber": mobile_number}
        try:
            response = requests.post(url, headers=config.API_HEADERS, json=payload)
            response.raise_for_status()
            print("OTP Sent Successfully.")
            return True
        except requests.exceptions.RequestException as e:
            print(f"Error requesting OTP: {e}")
            return False

    def generate_session(self, mobile_number, otp):
        """Validates OTP and generates a session."""
        url = f"{config.BASE_URL}/login/v3/customer/login"
        payload = {"mobileNumber": mobile_number, "password": otp} # 'password' is the OTP here
        try:
            response = requests.post(url, headers=config.API_HEADERS, json=payload)
            response.raise_for_status()
            data = response.json().get('data', {})
            
            self.sid = data.get('sid')
            self.auth_token = data.get('auth')
            
            if self.sid and self.auth_token:
                print("Session Generated Successfully.")
                # Update global headers for all future requests
                config.API_HEADERS['Authorization'] = f"bearer {self.sid}"
                config.API_HEADERS['Sid'] = self.sid # Some APIs might need this explicitly
                return True
            else:
                print("Failed to retrieve session tokens from response.")
                return False
        except requests.exceptions.RequestException as e:
            print(f"Error generating session: {e}")
            return False

# Example Usage in main app:
# auth = AuthHandler()
# auth.request_otp(config.MOBILE_NUMBER)
# user_otp = input("Enter OTP: ") # This would come from the UI
# if auth.generate_session(config.MOBILE_NUMBER, user_otp):
#     print("Login Successful!")
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END
📊 Phase 2: Instrument & Symbol Management

Objective: Fetch the master list of instruments and find the correct instrumentToken for a given option.

API Endpoint:

A downloadable CSV/JSON file. The exact URL should be confirmed from Kotak Neo developer resources (e.g., https://kapi.kotaksecurities.com/files/get-scrips-for-market-watch).

Strategy:

Download the instrument master file on application startup.

Load it into a searchable format (e.g., a list of dictionaries or a pandas DataFrame).

Create a function to find the instrumentToken based on underlying, expiry, strike, and type.

Code (symbol_manager.py):

Generated python
import requests
import csv
from io import StringIO

class SymbolManager:
    def __init__(self):
        self.instruments = []
        self.load_instruments()

    def load_instruments(self):
        """Downloads and loads the instrument master file."""
        # Replace with the actual URL from Kotak Neo
        url = "https://kapi.kotaksecurities.com/files/get-scrips-for-market-watch"
        try:
            response = requests.get(url)
            response.raise_for_status()
            
            # Assuming the file is a CSV
            csv_file = StringIO(response.text)
            reader = csv.DictReader(csv_file)
            self.instruments = [row for row in reader]
            print(f"Successfully loaded {len(self.instruments)} instruments.")
        except requests.exceptions.RequestException as e:
            print(f"Failed to download instrument file: {e}")

    def find_instrument_token(self, instrument, expiry, strike, option_type):
        """Finds the instrument token for a given option."""
        # Note: The exact field names and date format depend on the instrument file's structure.
        # This is a conceptual example. You will need to adapt it.
        # Example expiry format in file might be '10JUL25'
        
        for inst in self.instruments:
            # This logic is highly dependent on the CSV columns and format
            if (inst.get('instrument') == instrument and
                inst.get('strike') == str(strike) and
                inst.get('optionType') == option_type): # 'PE' or 'CE'
                # Add date matching logic here, which can be complex
                return inst.get('instrumentToken')
        
        print(f"Instrument token not found for {instrument} {strike} {option_type}")
        return None

# Example Usage:
# symbol_mgr = SymbolManager()
# token = symbol_mgr.find_instrument_token("NIFTY", "10-Jul-2025", 25500, "PE")
# if token:
#     print(f"Found token: {token}")
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END
📡 Phase 3: Real-Time Price Monitoring (WebSocket)

Objective: Get live, tick-by-tick data with minimal latency.

Strategy:

Use the websocket-client library to connect to the Kotak Neo feed endpoint.

Authenticate the WebSocket connection using the session tokens.

Subscribe to the instrumentTokens of the symbols in your watchlist.

The on_message handler will process incoming ticks and pass the LTP to the main application logic.

Code (websocket_handler.py):

Generated python
import websocket
import json
import threading
import config

class WebsocketClient:
    def __init__(self, auth_token, sid):
        # The WebSocket URL must be obtained from Kotak Neo documentation
        self.ws_url = "wss://wskapi.kotaksecurities.com/feed" 
        self.auth_token = auth_token
        self.sid = sid
        self.ws = None
        self.on_tick_callback = None # Callback to notify main app of a new tick

    def connect(self):
        """Establishes the WebSocket connection."""
        self.ws = websocket.WebSocketApp(
            self.ws_url,
            on_open=self.on_open,
            on_message=self.on_message,
            on_error=self.on_error,
            on_close=self.on_close,
            header={'Authorization': f"Bearer {self.sid}", 'Sid': self.sid}
        )
        # Run the WebSocket in a separate thread to avoid blocking the main UI
        wst = threading.Thread(target=self.ws.run_forever)
        wst.daemon = True
        wst.start()

    def on_open(self, ws):
        print("WebSocket Connection Opened.")
        # Automatically subscribe to default symbol or symbols from watchlist upon connection
        # self.subscribe(['INSTRUMENT_TOKEN_1', 'INSTRUMENT_TOKEN_2'])

    def on_message(self, ws, message):
        """Handles incoming tick data."""
        # The message format is specific to Kotak Neo. This is a sample structure.
        try:
            tick_data = json.loads(message)
            # Example tick structure: {"instrumentToken": "123", "lastPrice": 250.50}
            if 'instrumentToken' in tick_data and 'lastPrice' in tick_data:
                if self.on_tick_callback:
                    # Pass the parsed data to the main application
                    self.on_tick_callback(tick_data)
        except json.JSONDecodeError:
            print(f"Received non-JSON message: {message}")

    def on_error(self, ws, error):
        print(f"WebSocket Error: {error}")

    def on_close(self, ws, close_status_code, close_msg):
        print("WebSocket Connection Closed.")
        # Implement reconnection logic here

    def subscribe(self, instrument_tokens):
        """Subscribes to a list of instrument tokens."""
        if self.ws and self.ws.sock and self.ws.sock.connected:
            sub_payload = {
                "action": "subscribe",
                "instrumentTokens": instrument_tokens
            }
            self.ws.send(json.dumps(sub_payload))
            print(f"Subscribed to tokens: {instrument_tokens}")

# How to use this in the main app:
# def handle_new_tick(tick):
#     print(f"New Tick: {tick['instrumentToken']} -> LTP: {tick['lastPrice']}")
#     # This is where the trading logic will be triggered
#
# auth = AuthHandler() # Assuming login is done
# ws_client = WebsocketClient(auth.auth_token, auth.sid)
# ws_client.on_tick_callback = handle_new_tick
# ws_client.connect()
# 
# # Later, when user adds a symbol:
# # ws_client.subscribe([NEW_INSTRUMENT_TOKEN])
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END
🎯 Phase 4: Automated Trading Execution

Objective: Place orders automatically based on time and price triggers.

API Endpoint:

POST /v3/orders

Strategy:

Timed Entry: Use apscheduler to schedule a function call at exactly 9:19:58 AM. This function will place a MARKET buy order.

Price-Based Exit: The on_tick_callback function (from Phase 3) will be the entry point for this logic. When a new tick arrives, it will check if the price has hit the Stop-Loss or Take-Profit level.

State Management: Maintain a state for each active trade (e.g., buy_price, status='active'). Once an exit order is placed, update the status to prevent duplicate orders.

Code (order_manager.py):

Generated python
import requests
import config
from apscheduler.schedulers.background import BackgroundScheduler

class OrderManager:
    def __init__(self, auth_handler):
        self.auth_handler = auth_handler
        self.active_trades = {} # key: instrumentToken, value: {buy_price, status}
        
        # Scheduler for timed entry
        self.scheduler = BackgroundScheduler()
        self.scheduler.add_job(
            self.execute_timed_buy, 
            'cron', 
            day_of_week='mon-fri', 
            hour=9, 
            minute=19, 
            second=58
        )
        self.scheduler.start()

    def place_order(self, instrument_token, action, quantity):
        """Places a MARKET order."""
        url = f"{config.BASE_URL}/v3/orders"
        payload = {
            "instrumentToken": instrument_token,
            "transactionType": action, # "BUY" or "SELL"
            "quantity": quantity,
            "orderType": "MARKET",
            "product": "NORMAL" # Or as required
        }
        try:
            response = requests.post(url, headers=config.API_HEADERS, json=payload)
            response.raise_for_status()
            order_data = response.json()
            print(f"Order placed successfully: {order_data}")
            # You need to fetch tradebook to get the executed price
            return order_data
        except requests.exceptions.RequestException as e:
            print(f"Error placing order for {instrument_token}: {e}")
            return None

    def execute_timed_buy(self):
        print("Executing scheduled buy order at 9:19:58 AM...")
        # Get token for the default symbol
        # token = symbol_manager.find_instrument_token(...) 
        # self.place_order(token, "BUY", config.ORDER_QUANTITY)
        # After confirming execution, add to active_trades
        # self.active_trades[token] = {'buy_price': confirmed_buy_price, 'status': 'active'}

    def check_exit_conditions(self, tick_data):
        """Checks SL/TP triggers for an active trade."""
        token = tick_data['instrumentToken']
        ltp = tick_data['lastPrice']

        if token in self.active_trades and self.active_trades[token]['status'] == 'active':
            buy_price = self.active_trades[token]['buy_price']
            
            # Stop-Loss condition
            if ltp <= (buy_price - 1.0):
                print(f"Stop-Loss triggered for {token} at LTP {ltp}!")
                self.place_order(token, "SELL", config.ORDER_QUANTITY)
                self.active_trades[token]['status'] = 'closed' # Prevent re-triggers
            
            # Take-Profit condition
            elif ltp >= (buy_price + 1.0):
                print(f"Take-Profit triggered for {token} at LTP {ltp}!")
                self.place_order(token, "SELL", config.ORDER_QUANTITY)
                self.active_trades[token]['status'] = 'closed'

# In main app, connect this to the websocket callback:
# order_mgr = OrderManager(auth)
# ws_client.on_tick_callback = order_mgr.check_exit_conditions
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END
🖥️ Phase 5: UI, Portfolio, and Logging

Objective: Create a functional UI to display data and logs, and manage the portfolio.

API Endpoints:

GET /v3/portfolio/positions

GET /v3/orders/trades

UI Structure (Conceptual for PyQt5):

MainWindow: A main window with a dark theme.

Login Dialog: A popup dialog to handle OTP entry.

Symbol Management Panel:

QComboBox for NIFTY/BANKNIFTY.

QLineEdit for Strike, QDateEdit for Expiry.

QPushButton ("Add Symbol") to call symbol_manager.find_instrument_token() and websocket_client.subscribe().

Watchlist View:

QTableWidget to display subscribed symbols. Columns: Symbol, LTP.

The LTP cell should be updated by the on_tick_callback.

Positions View:

Another QTableWidget to display data from the /v3/portfolio/positions endpoint. Columns: Symbol, Qty, Avg Price, LTP, P&L.

This table should be refreshed periodically.

Log Panel:

A QTextEdit or QListWidget at the bottom where all print() statements are redirected. This provides real-time status feedback.

Code (portfolio_manager.py):```python
import requests
import config

def get_positions():
"""Fetches current open positions."""
url = f"{config.BASE_URL}/v3/portfolio/positions"
try:
response = requests.get(url, headers=config.API_HEADERS)
response.raise_for_status()
positions = response.json().get('data', [])
# This data will be sent to the UI to update the positions table
return positions
except requests.exceptions.RequestException as e:
print(f"Error fetching positions: {e}")
return None

def get_tradebook():
"""Fetches today's executed trades."""
url = f"{config.BASE_URL}/v3/orders/trades"
try:
response = requests.get(url, headers=config.API_HEADERS)
response.raise_for_status()
trades = response.json().get('data', [])
# This data will be sent to the UI to update the tradebook table
return trades
except requests.exceptions.RequestException as e:
print(f"Error fetching tradebook: {e}")
return None

Generated code
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END