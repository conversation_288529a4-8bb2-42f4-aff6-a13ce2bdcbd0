# logger.py - Logging system for the trading application

import logging
import logging.handlers
import sys
from datetime import datetime
from PyQt5.QtCore import QObject, pyqtSignal
import config

class QtLogHandler(logging.Handler, QObject):
    """Custom log handler that emits Qt signals for UI updates"""
    
    log_signal = pyqtSignal(str)
    
    def __init__(self):
        logging.Handler.__init__(self)
        QObject.__init__(self)
    
    def emit(self, record):
        """Emit log record as Qt signal"""
        try:
            msg = self.format(record)
            self.log_signal.emit(msg)
        except Exception:
            pass

class TradingLogger:
    """Centralized logging system for the trading application"""
    
    def __init__(self):
        self.logger = logging.getLogger('TradingBot')
        self.logger.setLevel(getattr(logging, config.LOG_LEVEL))
        
        # Remove existing handlers
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)
        
        # Create formatters
        detailed_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        simple_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        
        # File handler with rotation
        file_handler = logging.handlers.RotatingFileHandler(
            config.LOG_FILE,
            maxBytes=config.MAX_LOG_SIZE,
            backupCount=5
        )
        file_handler.setFormatter(detailed_formatter)
        self.logger.addHandler(file_handler)
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(simple_formatter)
        self.logger.addHandler(console_handler)
        
        # Qt handler for UI
        self.qt_handler = QtLogHandler()
        self.qt_handler.setFormatter(simple_formatter)
        self.logger.addHandler(self.qt_handler)
    
    def get_qt_handler(self):
        """Get the Qt log handler for connecting to UI"""
        return self.qt_handler
    
    def info(self, message):
        """Log info message"""
        self.logger.info(message)
    
    def warning(self, message):
        """Log warning message"""
        self.logger.warning(message)
    
    def error(self, message):
        """Log error message"""
        self.logger.error(message)
    
    def debug(self, message):
        """Log debug message"""
        self.logger.debug(message)
    
    def critical(self, message):
        """Log critical message"""
        self.logger.critical(message)

# Global logger instance
logger = TradingLogger()
