#!/usr/bin/env python3
# main.py - Entry point for the Kotak Neo Trading Bot

import sys
import os
import traceback
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def handle_exception(exc_type, exc_value, exc_traceback):
    """Global exception handler"""
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    
    error_msg = "".join(traceback.format_exception(exc_type, exc_value, exc_traceback))
    print(f"Uncaught exception: {error_msg}")
    
    # Try to show error dialog if QApplication exists
    app = QApplication.instance()
    if app:
        QMessageBox.critical(None, "Critical Error", 
                           f"An unexpected error occurred:\n\n{exc_value}\n\n"
                           f"Please check the log file for details.")

def check_dependencies():
    """Check if all required dependencies are available"""
    missing_deps = []
    
    try:
        import requests
    except ImportError:
        missing_deps.append("requests")
    
    try:
        import websocket
    except ImportError:
        missing_deps.append("websocket-client")
    
    try:
        from apscheduler.schedulers.background import BackgroundScheduler
    except ImportError:
        missing_deps.append("apscheduler")
    
    try:
        from PyQt5.QtWidgets import QApplication
    except ImportError:
        missing_deps.append("PyQt5")
    
    try:
        import pandas
    except ImportError:
        missing_deps.append("pandas")
    
    if missing_deps:
        print("Missing required dependencies:")
        for dep in missing_deps:
            print(f"  - {dep}")
        print("\nPlease install missing dependencies using:")
        print("pip install -r requirements.txt")
        return False
    
    return True

def setup_application():
    """Setup application environment"""
    # Set exception handler
    sys.excepthook = handle_exception
    
    # Enable high DPI scaling
    if hasattr(Qt, 'AA_EnableHighDpiScaling'):
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    
    if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

def main():
    """Main entry point"""
    print("Starting Kotak Neo Trading Bot...")
    
    # Check dependencies
    if not check_dependencies():
        input("Press Enter to exit...")
        return 1
    
    # Setup application
    setup_application()
    
    try:
        # Import and run the main application
        from trading_app import main as run_app
        return run_app()
        
    except ImportError as e:
        print(f"Import error: {e}")
        print("Please ensure all application files are present.")
        input("Press Enter to exit...")
        return 1
    
    except Exception as e:
        print(f"Failed to start application: {e}")
        traceback.print_exc()
        input("Press Enter to exit...")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
