# main_window.py - Main UI window for the trading application

import sys
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QTabWidget, QLabel, QPushButton, QLineEdit, QComboBox,
                            QTableWidget, QTableWidgetItem, QTextEdit, QGroupBox,
                            QGridLayout, QSpinBox, QDoubleSpinBox, QCheckBox,
                            QMessageBox, QDialog, QDialogButtonBox, QFormLayout,
                            QSplitter, QHeaderView, QFrame)
from PyQt5.QtCore import Qt, QTimer, pyqtSlot
from PyQt5.QtGui import QFont, QPalette, QColor, QIcon
import config
from logger import logger

class LoginDialog(QDialog):
    """Login dialog for OTP authentication"""

    def __init__(self, auth_handler, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Kotak Neo Login")
        self.setModal(True)
        self.setFixedSize(350, 200)

        self.auth_handler = auth_handler
        self.mobile_number = None
        self.otp = None

        self.setup_ui()
    
    def setup_ui(self):
        layout = QFormLayout()
        
        # Mobile number input
        self.mobile_edit = QLineEdit()
        self.mobile_edit.setPlaceholderText("Enter mobile number")
        self.mobile_edit.setText(config.config.get('mobile_number', ''))
        layout.addRow("Mobile Number:", self.mobile_edit)
        
        # OTP input (initially hidden)
        self.otp_edit = QLineEdit()
        self.otp_edit.setPlaceholderText("Enter OTP")
        self.otp_edit.setEchoMode(QLineEdit.Password)
        self.otp_edit.setVisible(False)
        self.otp_label = QLabel("OTP:")
        self.otp_label.setVisible(False)
        layout.addRow(self.otp_label, self.otp_edit)
        
        # Status label
        self.status_label = QLabel("")
        layout.addRow(self.status_label)
        
        # Buttons
        self.button_box = QDialogButtonBox()
        self.request_otp_btn = QPushButton("Request OTP")
        self.login_btn = QPushButton("Login")
        self.login_btn.setVisible(False)
        
        self.button_box.addButton(self.request_otp_btn, QDialogButtonBox.ActionRole)
        self.button_box.addButton(self.login_btn, QDialogButtonBox.AcceptRole)
        self.button_box.addButton(QDialogButtonBox.Cancel)
        
        layout.addRow(self.button_box)
        
        self.setLayout(layout)
        
        # Connect signals
        self.request_otp_btn.clicked.connect(self.request_otp)
        self.login_btn.clicked.connect(self.accept)
        self.button_box.rejected.connect(self.reject)
    
    def request_otp(self):
        mobile = self.mobile_edit.text().strip()
        if not mobile:
            self.status_label.setText("Please enter mobile number")
            return

        self.mobile_number = mobile
        self.status_label.setText("Requesting OTP...")

        # Actually request OTP through auth handler
        success, message = self.auth_handler.request_otp(mobile)

        if success:
            # Show OTP input
            self.otp_edit.setVisible(True)
            self.otp_label.setVisible(True)
            self.login_btn.setVisible(True)
            self.request_otp_btn.setVisible(False)

            self.status_label.setText("OTP sent! Please enter OTP.")
            self.otp_edit.setFocus()
        else:
            self.status_label.setText(f"Failed to send OTP: {message}")
    
    def accept(self):
        self.otp = self.otp_edit.text().strip()
        if not self.otp:
            self.status_label.setText("Please enter OTP")
            return
        
        super().accept()

class SymbolManagerWidget(QWidget):
    """Widget for managing trading symbols"""
    
    def __init__(self, symbol_manager, websocket_client):
        super().__init__()
        self.symbol_manager = symbol_manager
        self.websocket_client = websocket_client
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # Symbol selection
        selection_group = QGroupBox("Add Symbol")
        selection_layout = QGridLayout()
        
        # Instrument dropdown
        self.instrument_combo = QComboBox()
        self.instrument_combo.addItems(["NIFTY", "BANKNIFTY"])
        selection_layout.addWidget(QLabel("Instrument:"), 0, 0)
        selection_layout.addWidget(self.instrument_combo, 0, 1)
        
        # Expiry date
        self.expiry_edit = QLineEdit()
        self.expiry_edit.setPlaceholderText("DD-MMM-YYYY")
        self.expiry_edit.setText(config.DEFAULT_SYMBOL['expiry'])
        selection_layout.addWidget(QLabel("Expiry:"), 0, 2)
        selection_layout.addWidget(self.expiry_edit, 0, 3)
        
        # Strike price
        self.strike_spin = QSpinBox()
        self.strike_spin.setRange(1000, 50000)
        self.strike_spin.setValue(config.DEFAULT_SYMBOL['strike'])
        self.strike_spin.setSingleStep(50)
        selection_layout.addWidget(QLabel("Strike:"), 1, 0)
        selection_layout.addWidget(self.strike_spin, 1, 1)
        
        # Option type
        self.option_combo = QComboBox()
        self.option_combo.addItems(["PE", "CE"])
        selection_layout.addWidget(QLabel("Type:"), 1, 2)
        selection_layout.addWidget(self.option_combo, 1, 3)
        
        # Add button
        self.add_btn = QPushButton("Add Symbol")
        self.add_btn.clicked.connect(self.add_symbol)
        selection_layout.addWidget(self.add_btn, 2, 0, 1, 4)
        
        selection_group.setLayout(selection_layout)
        layout.addWidget(selection_group)
        
        # Watchlist table
        watchlist_group = QGroupBox("Watchlist")
        watchlist_layout = QVBoxLayout()
        
        self.watchlist_table = QTableWidget()
        self.watchlist_table.setColumnCount(4)
        self.watchlist_table.setHorizontalHeaderLabels(["Symbol", "LTP", "Change", "Action"])
        self.watchlist_table.horizontalHeader().setStretchLastSection(True)
        
        watchlist_layout.addWidget(self.watchlist_table)
        watchlist_group.setLayout(watchlist_layout)
        layout.addWidget(watchlist_group)
        
        self.setLayout(layout)
    
    def add_symbol(self):
        instrument = self.instrument_combo.currentText()
        expiry = self.expiry_edit.text().strip()
        strike = self.strike_spin.value()
        option_type = self.option_combo.currentText()
        
        if not expiry:
            QMessageBox.warning(self, "Error", "Please enter expiry date")
            return
        
        # Find instrument token
        symbol_info = self.symbol_manager.find_instrument_token(
            instrument, expiry, strike, option_type
        )
        
        if symbol_info:
            # Add to watchlist
            if self.symbol_manager.add_to_watchlist(symbol_info):
                self.update_watchlist_table()
                
                # Subscribe to WebSocket
                if self.websocket_client.is_connected:
                    self.websocket_client.subscribe([symbol_info['instrument_token']])
                
                logger.info(f"Added symbol: {symbol_info['trading_symbol']}")
            else:
                QMessageBox.information(self, "Info", "Symbol already in watchlist")
        else:
            QMessageBox.warning(self, "Error", "Symbol not found")
    
    def update_watchlist_table(self):
        watchlist = self.symbol_manager.get_watchlist()
        self.watchlist_table.setRowCount(len(watchlist))
        
        for row, symbol_info in enumerate(watchlist):
            # Symbol name
            self.watchlist_table.setItem(row, 0, QTableWidgetItem(symbol_info['trading_symbol']))
            
            # LTP (will be updated by price updates)
            self.watchlist_table.setItem(row, 1, QTableWidgetItem("--"))
            
            # Change
            self.watchlist_table.setItem(row, 2, QTableWidgetItem("--"))
            
            # Remove button
            remove_btn = QPushButton("Remove")
            remove_btn.clicked.connect(lambda checked, token=symbol_info['instrument_token']: self.remove_symbol(token))
            self.watchlist_table.setCellWidget(row, 3, remove_btn)
    
    def remove_symbol(self, instrument_token):
        if self.symbol_manager.remove_from_watchlist(instrument_token):
            self.update_watchlist_table()
            
            # Unsubscribe from WebSocket
            if self.websocket_client.is_connected:
                self.websocket_client.unsubscribe([instrument_token])
    
    def update_price(self, tick_data):
        """Update price in watchlist table"""
        instrument_token = tick_data['instrument_token']
        last_price = tick_data['last_price']
        change = tick_data.get('change', 0)
        
        # Find row for this instrument
        for row in range(self.watchlist_table.rowCount()):
            symbol_item = self.watchlist_table.item(row, 0)
            if symbol_item:
                symbol_info = self.symbol_manager.get_symbol_by_token(instrument_token)
                if symbol_info and symbol_item.text() == symbol_info['trading_symbol']:
                    # Update LTP
                    ltp_item = QTableWidgetItem(f"₹{last_price:.2f}")
                    if change > 0:
                        ltp_item.setBackground(QColor(0, 255, 0, 50))  # Light green
                    elif change < 0:
                        ltp_item.setBackground(QColor(255, 0, 0, 50))  # Light red
                    self.watchlist_table.setItem(row, 1, ltp_item)
                    
                    # Update change
                    change_text = f"₹{change:+.2f}"
                    change_item = QTableWidgetItem(change_text)
                    if change > 0:
                        change_item.setForeground(QColor(0, 128, 0))  # Green
                    elif change < 0:
                        change_item.setForeground(QColor(255, 0, 0))  # Red
                    self.watchlist_table.setItem(row, 2, change_item)
                    break

class TradingControlWidget(QWidget):
    """Widget for trading controls and parameters"""
    
    def __init__(self, order_manager):
        super().__init__()
        self.order_manager = order_manager
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # Trading parameters
        params_group = QGroupBox("Trading Parameters")
        params_layout = QGridLayout()
        
        # Quantity
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setRange(1, 1000)
        self.quantity_spin.setValue(config.ORDER_QUANTITY)
        params_layout.addWidget(QLabel("Quantity:"), 0, 0)
        params_layout.addWidget(self.quantity_spin, 0, 1)
        
        # Buy time
        self.buy_time_edit = QLineEdit()
        self.buy_time_edit.setText(config.DEFAULT_BUY_TIME)
        self.buy_time_edit.setPlaceholderText("HH:MM:SS")
        params_layout.addWidget(QLabel("Buy Time:"), 0, 2)
        params_layout.addWidget(self.buy_time_edit, 0, 3)
        
        # Stop loss
        self.stop_loss_spin = QDoubleSpinBox()
        self.stop_loss_spin.setRange(0.1, 100.0)
        self.stop_loss_spin.setValue(config.STOP_LOSS_AMOUNT)
        self.stop_loss_spin.setSingleStep(0.5)
        params_layout.addWidget(QLabel("Stop Loss (₹):"), 1, 0)
        params_layout.addWidget(self.stop_loss_spin, 1, 1)
        
        # Profit target
        self.profit_target_spin = QDoubleSpinBox()
        self.profit_target_spin.setRange(0.1, 100.0)
        self.profit_target_spin.setValue(config.PROFIT_TARGET_AMOUNT)
        self.profit_target_spin.setSingleStep(0.5)
        params_layout.addWidget(QLabel("Profit Target (₹):"), 1, 2)
        params_layout.addWidget(self.profit_target_spin, 1, 3)
        
        # Auto trading checkbox
        self.auto_trading_check = QCheckBox("Enable Auto Trading")
        params_layout.addWidget(self.auto_trading_check, 2, 0, 1, 2)
        
        # Update button
        update_btn = QPushButton("Update Parameters")
        update_btn.clicked.connect(self.update_parameters)
        params_layout.addWidget(update_btn, 2, 2, 1, 2)
        
        params_group.setLayout(params_layout)
        layout.addWidget(params_group)
        
        # Manual trading controls
        manual_group = QGroupBox("Manual Trading")
        manual_layout = QHBoxLayout()
        
        self.buy_btn = QPushButton("Manual Buy")
        self.buy_btn.clicked.connect(self.manual_buy)
        manual_layout.addWidget(self.buy_btn)
        
        self.sell_btn = QPushButton("Manual Sell")
        self.sell_btn.clicked.connect(self.manual_sell)
        manual_layout.addWidget(self.sell_btn)
        
        manual_group.setLayout(manual_layout)
        layout.addWidget(manual_group)
        
        self.setLayout(layout)
    
    def update_parameters(self):
        quantity = self.quantity_spin.value()
        buy_time = self.buy_time_edit.text().strip()
        stop_loss = self.stop_loss_spin.value()
        profit_target = self.profit_target_spin.value()
        auto_trading = self.auto_trading_check.isChecked()
        
        self.order_manager.set_trading_parameters(
            quantity=quantity,
            stop_loss=stop_loss,
            profit_target=profit_target,
            buy_time=buy_time
        )
        
        self.order_manager.enable_auto_trading(auto_trading)
        
        QMessageBox.information(self, "Success", "Trading parameters updated")
    
    def manual_buy(self):
        # This would need to be connected to symbol selection
        QMessageBox.information(self, "Info", "Select a symbol from watchlist first")
    
    def manual_sell(self):
        # This would need to be connected to position selection
        QMessageBox.information(self, "Info", "Select a position to sell")

class PositionsWidget(QWidget):
    """Widget for displaying portfolio positions"""

    def __init__(self):
        super().__init__()
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout()

        # P&L display
        pnl_layout = QHBoxLayout()
        self.pnl_label = QLabel("Daily P&L: ₹0.00")
        self.pnl_label.setFont(QFont("Arial", 14, QFont.Bold))
        pnl_layout.addWidget(self.pnl_label)
        pnl_layout.addStretch()

        refresh_btn = QPushButton("Refresh")
        refresh_btn.clicked.connect(self.refresh_positions)
        pnl_layout.addWidget(refresh_btn)

        layout.addLayout(pnl_layout)

        # Positions table
        self.positions_table = QTableWidget()
        self.positions_table.setColumnCount(8)
        self.positions_table.setHorizontalHeaderLabels([
            "Symbol", "Qty", "Avg Price", "LTP", "Change", "P&L", "Day P&L", "Value"
        ])
        self.positions_table.horizontalHeader().setStretchLastSection(True)

        layout.addWidget(self.positions_table)
        self.setLayout(layout)

    def update_positions(self, positions):
        """Update positions table"""
        self.positions_table.setRowCount(len(positions))

        for row, position in enumerate(positions):
            self.positions_table.setItem(row, 0, QTableWidgetItem(position['trading_symbol']))
            self.positions_table.setItem(row, 1, QTableWidgetItem(str(position['quantity'])))
            self.positions_table.setItem(row, 2, QTableWidgetItem(f"₹{position['average_price']:.2f}"))
            self.positions_table.setItem(row, 3, QTableWidgetItem(f"₹{position['last_price']:.2f}"))

            # Change with color
            change_item = QTableWidgetItem(f"₹{position['change']:+.2f}")
            if position['change'] > 0:
                change_item.setForeground(QColor(0, 128, 0))
            elif position['change'] < 0:
                change_item.setForeground(QColor(255, 0, 0))
            self.positions_table.setItem(row, 4, change_item)

            # P&L with color
            pnl_item = QTableWidgetItem(f"₹{position['pnl']:+.2f}")
            if position['pnl'] > 0:
                pnl_item.setForeground(QColor(0, 128, 0))
            elif position['pnl'] < 0:
                pnl_item.setForeground(QColor(255, 0, 0))
            self.positions_table.setItem(row, 5, pnl_item)

            # Day P&L
            day_pnl_item = QTableWidgetItem(f"₹{position['day_pnl']:+.2f}")
            if position['day_pnl'] > 0:
                day_pnl_item.setForeground(QColor(0, 128, 0))
            elif position['day_pnl'] < 0:
                day_pnl_item.setForeground(QColor(255, 0, 0))
            self.positions_table.setItem(row, 6, day_pnl_item)

            self.positions_table.setItem(row, 7, QTableWidgetItem(f"₹{position['value']:.2f}"))

    def update_pnl(self, pnl):
        """Update P&L display"""
        self.pnl_label.setText(f"Daily P&L: ₹{pnl:+.2f}")
        if pnl > 0:
            self.pnl_label.setStyleSheet("color: green;")
        elif pnl < 0:
            self.pnl_label.setStyleSheet("color: red;")
        else:
            self.pnl_label.setStyleSheet("color: black;")

    def refresh_positions(self):
        """Signal to refresh positions"""
        # This would be connected to portfolio manager
        pass

class TradesWidget(QWidget):
    """Widget for displaying trade history"""

    def __init__(self):
        super().__init__()
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout()

        # Controls
        controls_layout = QHBoxLayout()

        refresh_btn = QPushButton("Refresh")
        refresh_btn.clicked.connect(self.refresh_trades)
        controls_layout.addWidget(refresh_btn)

        export_btn = QPushButton("Export CSV")
        export_btn.clicked.connect(self.export_trades)
        controls_layout.addWidget(export_btn)

        controls_layout.addStretch()
        layout.addLayout(controls_layout)

        # Trades table
        self.trades_table = QTableWidget()
        self.trades_table.setColumnCount(8)
        self.trades_table.setHorizontalHeaderLabels([
            "Time", "Symbol", "Type", "Qty", "Price", "Value", "Brokerage", "P&L"
        ])
        self.trades_table.horizontalHeader().setStretchLastSection(True)

        layout.addWidget(self.trades_table)
        self.setLayout(layout)

    def update_trades(self, trades):
        """Update trades table"""
        self.trades_table.setRowCount(len(trades))

        for row, trade in enumerate(trades):
            self.trades_table.setItem(row, 0, QTableWidgetItem(trade['trade_time'].strftime("%H:%M:%S")))
            self.trades_table.setItem(row, 1, QTableWidgetItem(trade['trading_symbol']))

            # Transaction type with color
            type_item = QTableWidgetItem(trade['transaction_type'])
            if trade['transaction_type'] == 'BUY':
                type_item.setForeground(QColor(0, 128, 0))
            else:
                type_item.setForeground(QColor(255, 0, 0))
            self.trades_table.setItem(row, 2, type_item)

            self.trades_table.setItem(row, 3, QTableWidgetItem(str(trade['quantity'])))
            self.trades_table.setItem(row, 4, QTableWidgetItem(f"₹{trade['price']:.2f}"))
            self.trades_table.setItem(row, 5, QTableWidgetItem(f"₹{trade['value']:.2f}"))
            self.trades_table.setItem(row, 6, QTableWidgetItem(f"₹{trade['brokerage']:.2f}"))

            # Calculate simple P&L (this is simplified)
            pnl = 0  # Would need proper calculation
            self.trades_table.setItem(row, 7, QTableWidgetItem(f"₹{pnl:.2f}"))

    def refresh_trades(self):
        """Signal to refresh trades"""
        pass

    def export_trades(self):
        """Export trades to CSV"""
        pass

class LogWidget(QWidget):
    """Widget for displaying application logs"""

    def __init__(self):
        super().__init__()
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout()

        # Controls
        controls_layout = QHBoxLayout()

        clear_btn = QPushButton("Clear")
        clear_btn.clicked.connect(self.clear_logs)
        controls_layout.addWidget(clear_btn)

        controls_layout.addStretch()
        layout.addLayout(controls_layout)

        # Log display
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        # Note: setMaximumBlockCount not available in all PyQt5 versions

        layout.addWidget(self.log_text)
        self.setLayout(layout)

    def add_log_message(self, message):
        """Add log message to display"""
        self.log_text.append(message)
        # Auto-scroll to bottom
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def clear_logs(self):
        """Clear log display"""
        self.log_text.clear()
