# neo_auth_handler.py - Authentication handler using official Kotak Neo SDK

import os
import json
from datetime import datetime, timed<PERSON>ta
from neo_api_client import NeoAPI
import config
from logger import logger

class NeoAuthHandler:
    """Handles authentication with Kotak Neo API using official SDK"""
    
    def __init__(self):
        self.client = None
        self.is_logged_in = False
        self.session_data = {}
        self.mobile_number = None
        self.load_session()
        self.initialize_client()
    
    def initialize_client(self):
        """Initialize the Neo API client"""
        try:
            # Get credentials from config
            consumer_key = config.config.get('consumer_key', '')
            consumer_secret = config.config.get('consumer_secret', '')
            neo_fin_key = config.config.get('neo_fin_key', '')
            
            if not consumer_key or not consumer_secret:
                logger.error("Consumer key/secret not found in configuration")
                return False
            
            # Initialize client with credentials
            self.client = NeoAPI(
                consumer_key=consumer_key,
                consumer_secret=consumer_secret,
                environment='prod',  # Use 'uat' for testing, 'prod' for live
                neo_fin_key=neo_fin_key
            )
            
            logger.info("Neo API client initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing Neo API client: {e}")
            return False
    
    def load_session(self):
        """Load existing session from file if valid"""
        try:
            if os.path.exists(config.SESSION_FILE):
                with open(config.SESSION_FILE, 'r') as f:
                    session_data = json.load(f)
                
                # Check if session is still valid
                expiry_str = session_data.get('expiry')
                if expiry_str:
                    expiry = datetime.fromisoformat(expiry_str)
                    if datetime.now() < expiry:
                        self.session_data = session_data
                        self.mobile_number = session_data.get('mobile_number')
                        self.is_logged_in = True
                        logger.info("Loaded existing valid session")
                        return True
                
                # Session expired, remove file
                os.remove(config.SESSION_FILE)
                logger.info("Existing session expired, removed session file")
        except Exception as e:
            logger.error(f"Error loading session: {e}")
        
        return False
    
    def save_session(self):
        """Save current session to file"""
        try:
            session_data = {
                'mobile_number': self.mobile_number,
                'expiry': (datetime.now() + timedelta(hours=23)).isoformat(),
                'logged_in': self.is_logged_in
            }
            with open(config.SESSION_FILE, 'w') as f:
                json.dump(session_data, f, indent=2)
            logger.info("Session saved successfully")
        except Exception as e:
            logger.error(f"Error saving session: {e}")
    
    def request_otp(self, mobile_number):
        """Request OTP for the given mobile number"""
        if not self.client:
            if not self.initialize_client():
                return False, "Failed to initialize API client"

        self.mobile_number = mobile_number

        try:
            logger.info(f"Requesting OTP for mobile: {mobile_number}")

            # Get trading password from config
            trading_password = config.config.get('trading_password', '')
            if not trading_password:
                return False, "Trading password not configured"

            # Use the correct login method that triggers OTP
            result = self.client.login(mobilenumber=mobile_number, password=trading_password)

            if result:
                logger.info("OTP sent successfully to mobile")
                return True, "OTP sent to your mobile number"
            else:
                return False, "Failed to send OTP"

        except Exception as e:
            error_msg = f"Error requesting OTP: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def validate_otp(self, otp):
        """Validate OTP and complete 2FA"""
        if not self.client:
            return False, "API client not initialized"

        if not self.mobile_number:
            return False, "Mobile number not set"

        try:
            logger.info("Completing 2FA with OTP")

            # Complete 2FA with OTP (login was already done in request_otp)
            session_result = self.client.session_2fa(OTP=otp)

            if session_result:
                self.is_logged_in = True
                self.save_session()
                logger.info("Login successful - 2FA completed")
                return True, "Login successful"
            else:
                error_msg = "Invalid OTP or 2FA failed"
                logger.error(error_msg)
                return False, error_msg

        except Exception as e:
            error_msg = f"Error validating OTP: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def is_authenticated(self):
        """Check if user is currently authenticated"""
        if not self.is_logged_in or not self.client:
            return False
        
        # Check session expiry
        if self.session_data:
            expiry_str = self.session_data.get('expiry')
            if expiry_str:
                expiry = datetime.fromisoformat(expiry_str)
                if datetime.now() >= expiry:
                    logger.info("Session expired")
                    self.logout()
                    return False
        
        return True
    
    def logout(self):
        """Clear session data and logout"""
        try:
            if self.client and self.is_logged_in:
                self.client.logout()
        except Exception as e:
            logger.error(f"Error during logout: {e}")
        
        self.is_logged_in = False
        self.session_data = {}
        self.mobile_number = None
        
        # Remove session file
        try:
            if os.path.exists(config.SESSION_FILE):
                os.remove(config.SESSION_FILE)
        except Exception as e:
            logger.error(f"Error removing session file: {e}")
        
        logger.info("Logged out successfully")
    
    def verify_connection(self):
        """Verify API connection by making a test call"""
        if not self.is_authenticated():
            return False, "Not authenticated"
        
        try:
            # Test with positions call
            positions = self.client.positions()
            logger.info("API connection verified successfully")
            return True, "Connection verified"
                
        except Exception as e:
            error_msg = f"Connection verification failed: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def get_client(self):
        """Get the authenticated Neo API client"""
        if self.is_authenticated():
            return self.client
        return None
