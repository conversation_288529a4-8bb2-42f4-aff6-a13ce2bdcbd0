# order_manager.py - Order management and automated trading engine

import requests
import json
from datetime import datetime, time
from apscheduler.schedulers.background import BackgroundScheduler
from PyQt5.QtCore import QObject, pyqtSignal
import config
from logger import logger

class OrderManager(QObject):
    """Manages order placement and automated trading logic"""
    
    # Qt signals
    order_placed = pyqtSignal(dict)
    order_executed = pyqtSignal(dict)
    trade_completed = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, auth_handler, symbol_manager):
        super().__init__()
        self.auth_handler = auth_handler
        self.symbol_manager = symbol_manager
        self.scheduler = BackgroundScheduler()
        self.active_trades = {}  # {instrument_token: trade_info}
        self.scheduled_jobs = {}
        
        # Trading parameters
        self.auto_trading_enabled = False
        self.buy_time = config.DEFAULT_BUY_TIME
        self.quantity = config.ORDER_QUANTITY
        self.stop_loss_amount = config.STOP_LOSS_AMOUNT
        self.profit_target_amount = config.PROFIT_TARGET_AMOUNT
        
        self.scheduler.start()
        logger.info("Order manager initialized")
    
    def place_order(self, instrument_token, transaction_type, quantity, order_type="MKT", product="MIS"):
        """Place an order using Neo API client"""
        if not self.auth_handler.is_authenticated():
            error_msg = "Not authenticated - cannot place order"
            logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return None

        try:
            # Get the authenticated client
            client = self.auth_handler.get_client()
            if not client:
                error_msg = "No authenticated client available"
                logger.error(error_msg)
                self.error_occurred.emit(error_msg)
                return None

            # Get symbol info
            symbol_info = self.symbol_manager.get_symbol_by_token(instrument_token)
            trading_symbol = symbol_info['trading_symbol'] if symbol_info else instrument_token

            logger.info(f"Placing {transaction_type} order for {trading_symbol}: {quantity} qty")

            # Use the SDK to place order
            result = client.place_order(
                exchange_segment="nse_fo",
                product=product,
                price="0",  # For market orders
                order_type=order_type,
                quantity=str(quantity),
                validity="DAY",
                trading_symbol=trading_symbol,
                transaction_type=transaction_type,
                amo="NO",
                disclosed_quantity="0",
                market_protection="0",
                pf="N",
                trigger_price="0",
                tag=None
            )

            if result and result.get('stat') == 'ok':
                order_data = result.get('data', {})
                order_id = order_data.get('nOrdNo')
                
                order_info = {
                    'order_id': order_id,
                    'instrument_token': instrument_token,
                    'trading_symbol': trading_symbol,
                    'transaction_type': transaction_type,
                    'quantity': quantity,
                    'order_type': order_type,
                    'product': product,
                    'timestamp': datetime.now(),
                    'status': 'PLACED'
                }
                
                logger.info(f"Order placed successfully: {order_id}")
                self.order_placed.emit(order_info)
                
                # Check order status after a short delay
                self.scheduler.add_job(
                    self._check_order_status,
                    'date',
                    run_date=datetime.now().replace(second=datetime.now().second + 2),
                    args=[order_id, order_info]
                )
                
                return order_info
            else:
                error_msg = result.get('message', 'Order placement failed')
                logger.error(f"Order placement failed: {error_msg}")
                self.error_occurred.emit(error_msg)
                return None
                
        except requests.exceptions.RequestException as e:
            error_msg = f"Network error placing order: {e}"
            logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return None
        except Exception as e:
            error_msg = f"Error placing order: {e}"
            logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return None
    
    def _check_order_status(self, order_id, order_info):
        """Check the status of a placed order"""
        try:
            url = f"{config.BASE_URL}/v3/orders/{order_id}"
            response = requests.get(url, headers=config.API_HEADERS, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            if result.get('status') == 'success':
                order_data = result.get('data', {})
                order_status = order_data.get('orderStatus')
                
                if order_status == 'COMPLETE':
                    # Order executed
                    executed_price = float(order_data.get('averagePrice', 0))
                    executed_qty = int(order_data.get('filledQuantity', 0))
                    
                    execution_info = {
                        **order_info,
                        'status': 'EXECUTED',
                        'executed_price': executed_price,
                        'executed_quantity': executed_qty,
                        'execution_time': datetime.now()
                    }
                    
                    logger.info(f"Order executed: {order_id} at ₹{executed_price}")
                    self.order_executed.emit(execution_info)
                    
                    # If this was a buy order, start monitoring for exit
                    if order_info['transaction_type'] == 'BUY':
                        self._start_trade_monitoring(execution_info)
                    
                elif order_status in ['REJECTED', 'CANCELLED']:
                    error_msg = f"Order {order_status.lower()}: {order_data.get('rejectionReason', 'Unknown reason')}"
                    logger.error(error_msg)
                    self.error_occurred.emit(error_msg)
                
                else:
                    # Order still pending, check again
                    self.scheduler.add_job(
                        self._check_order_status,
                        'date',
                        run_date=datetime.now().replace(second=datetime.now().second + 2),
                        args=[order_id, order_info]
                    )
            
        except Exception as e:
            logger.error(f"Error checking order status: {e}")
    
    def _start_trade_monitoring(self, execution_info):
        """Start monitoring a trade for stop-loss and profit target"""
        instrument_token = execution_info['instrument_token']
        
        trade_info = {
            'instrument_token': instrument_token,
            'trading_symbol': execution_info['trading_symbol'],
            'buy_price': execution_info['executed_price'],
            'quantity': execution_info['executed_quantity'],
            'buy_time': execution_info['execution_time'],
            'status': 'ACTIVE',
            'stop_loss_price': execution_info['executed_price'] - self.stop_loss_amount,
            'profit_target_price': execution_info['executed_price'] + self.profit_target_amount
        }
        
        self.active_trades[instrument_token] = trade_info
        logger.info(f"Started monitoring trade: {trade_info['trading_symbol']} "
                   f"SL: ₹{trade_info['stop_loss_price']:.2f} "
                   f"TP: ₹{trade_info['profit_target_price']:.2f}")
    
    def check_exit_conditions(self, tick_data):
        """Check if any active trades should be exited based on price"""
        instrument_token = tick_data['instrument_token']
        current_price = tick_data['last_price']
        
        if instrument_token in self.active_trades:
            trade = self.active_trades[instrument_token]
            
            if trade['status'] == 'ACTIVE':
                # Check stop-loss condition
                if current_price <= trade['stop_loss_price']:
                    logger.info(f"Stop-loss triggered for {trade['trading_symbol']} "
                               f"at ₹{current_price} (SL: ₹{trade['stop_loss_price']:.2f})")
                    self._execute_exit_order(trade, 'STOP_LOSS', current_price)
                
                # Check profit target condition
                elif current_price >= trade['profit_target_price']:
                    logger.info(f"Profit target hit for {trade['trading_symbol']} "
                               f"at ₹{current_price} (TP: ₹{trade['profit_target_price']:.2f})")
                    self._execute_exit_order(trade, 'PROFIT_TARGET', current_price)
    
    def _execute_exit_order(self, trade, exit_reason, trigger_price):
        """Execute exit order for a trade"""
        # Mark trade as exiting to prevent duplicate orders
        trade['status'] = 'EXITING'
        trade['exit_reason'] = exit_reason
        trade['trigger_price'] = trigger_price
        
        # Place sell order
        exit_order = self.place_order(
            trade['instrument_token'],
            'SELL',
            trade['quantity']
        )
        
        if exit_order:
            trade['exit_order_id'] = exit_order['order_id']
            trade['exit_time'] = datetime.now()
            
            # The order status check will handle completion
            self.scheduler.add_job(
                self._check_exit_completion,
                'date',
                run_date=datetime.now().replace(second=datetime.now().second + 3),
                args=[trade]
            )
    
    def _check_exit_completion(self, trade):
        """Check if exit order is completed and calculate P&L"""
        try:
            url = f"{config.BASE_URL}/v3/orders/{trade['exit_order_id']}"
            response = requests.get(url, headers=config.API_HEADERS, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            if result.get('status') == 'success':
                order_data = result.get('data', {})
                order_status = order_data.get('orderStatus')
                
                if order_status == 'COMPLETE':
                    exit_price = float(order_data.get('averagePrice', 0))
                    
                    # Calculate P&L
                    pnl = (exit_price - trade['buy_price']) * trade['quantity']
                    
                    trade.update({
                        'status': 'COMPLETED',
                        'exit_price': exit_price,
                        'pnl': pnl,
                        'completion_time': datetime.now()
                    })
                    
                    logger.info(f"Trade completed: {trade['trading_symbol']} "
                               f"P&L: ₹{pnl:.2f} ({trade['exit_reason']})")
                    
                    self.trade_completed.emit(trade)
                    
                    # Remove from active trades
                    self.active_trades.pop(trade['instrument_token'], None)
                
                elif order_status in ['REJECTED', 'CANCELLED']:
                    # Exit order failed, mark trade as active again
                    trade['status'] = 'ACTIVE'
                    error_msg = f"Exit order failed for {trade['trading_symbol']}"
                    logger.error(error_msg)
                    self.error_occurred.emit(error_msg)
                
                else:
                    # Still pending, check again
                    self.scheduler.add_job(
                        self._check_exit_completion,
                        'date',
                        run_date=datetime.now().replace(second=datetime.now().second + 2),
                        args=[trade]
                    )
        
        except Exception as e:
            logger.error(f"Error checking exit completion: {e}")
            # Reset trade status
            trade['status'] = 'ACTIVE'
    
    def schedule_buy_order(self, instrument_token, buy_time_str=None):
        """Schedule a buy order for specific time"""
        if not buy_time_str:
            buy_time_str = self.buy_time
        
        try:
            # Parse time string (HH:MM:SS)
            buy_time_obj = datetime.strptime(buy_time_str, "%H:%M:%S").time()
            
            # Schedule for today (or next trading day if time has passed)
            now = datetime.now()
            schedule_date = now.replace(
                hour=buy_time_obj.hour,
                minute=buy_time_obj.minute,
                second=buy_time_obj.second,
                microsecond=0
            )
            
            # If time has passed today, schedule for tomorrow
            if schedule_date <= now:
                schedule_date = schedule_date.replace(day=schedule_date.day + 1)
            
            # Remove existing job if any
            job_id = f"buy_order_{instrument_token}"
            if job_id in self.scheduled_jobs:
                self.scheduler.remove_job(job_id)
            
            # Schedule the job
            job = self.scheduler.add_job(
                self._execute_scheduled_buy,
                'date',
                run_date=schedule_date,
                args=[instrument_token],
                id=job_id
            )
            
            self.scheduled_jobs[job_id] = job
            
            symbol_info = self.symbol_manager.get_symbol_by_token(instrument_token)
            symbol_name = symbol_info['trading_symbol'] if symbol_info else instrument_token
            
            logger.info(f"Scheduled buy order for {symbol_name} at {schedule_date}")
            return True
            
        except Exception as e:
            error_msg = f"Error scheduling buy order: {e}"
            logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return False
    
    def _execute_scheduled_buy(self, instrument_token):
        """Execute scheduled buy order"""
        logger.info("Executing scheduled buy order...")
        
        if self.auto_trading_enabled:
            order = self.place_order(instrument_token, 'BUY', self.quantity)
            if order:
                logger.info("Scheduled buy order placed successfully")
            else:
                logger.error("Failed to place scheduled buy order")
        else:
            logger.warning("Auto trading disabled - skipping scheduled buy order")
    
    def cancel_scheduled_orders(self):
        """Cancel all scheduled orders"""
        for job_id in list(self.scheduled_jobs.keys()):
            try:
                self.scheduler.remove_job(job_id)
                del self.scheduled_jobs[job_id]
            except Exception as e:
                logger.error(f"Error canceling job {job_id}: {e}")
        
        logger.info("All scheduled orders cancelled")
    
    def get_active_trades(self):
        """Get list of active trades"""
        return list(self.active_trades.values())
    
    def set_trading_parameters(self, quantity=None, stop_loss=None, profit_target=None, buy_time=None):
        """Update trading parameters"""
        if quantity is not None:
            self.quantity = quantity
        if stop_loss is not None:
            self.stop_loss_amount = stop_loss
        if profit_target is not None:
            self.profit_target_amount = profit_target
        if buy_time is not None:
            self.buy_time = buy_time
        
        logger.info(f"Trading parameters updated: Qty={self.quantity}, "
                   f"SL=₹{self.stop_loss_amount}, TP=₹{self.profit_target_amount}")
    
    def enable_auto_trading(self, enabled=True):
        """Enable or disable auto trading"""
        self.auto_trading_enabled = enabled
        status = "enabled" if enabled else "disabled"
        logger.info(f"Auto trading {status}")
    
    def shutdown(self):
        """Shutdown order manager"""
        self.cancel_scheduled_orders()
        self.scheduler.shutdown()
        logger.info("Order manager shutdown")
