# portfolio_manager.py - Portfolio and trade management

import requests
from datetime import datetime, timedelta
from PyQt5.QtCore import QObject, pyqtSignal, QTimer
import config
from logger import logger

class PortfolioManager(QObject):
    """Manages portfolio positions and trade history"""
    
    # Qt signals
    positions_updated = pyqtSignal(list)
    trades_updated = pyqtSignal(list)
    pnl_updated = pyqtSignal(float)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, auth_handler):
        super().__init__()
        self.auth_handler = auth_handler
        self.positions = []
        self.trades = []
        self.daily_pnl = 0.0
        
        # Timer for periodic updates
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.refresh_data)
        self.update_interval = 5000  # 5 seconds
        
        logger.info("Portfolio manager initialized")
    
    def start_monitoring(self):
        """Start periodic portfolio monitoring"""
        if self.auth_handler.is_authenticated():
            self.refresh_data()
            self.update_timer.start(self.update_interval)
            logger.info("Portfolio monitoring started")
        else:
            logger.warning("Cannot start monitoring - not authenticated")
    
    def stop_monitoring(self):
        """Stop periodic portfolio monitoring"""
        self.update_timer.stop()
        logger.info("Portfolio monitoring stopped")
    
    def refresh_data(self):
        """Refresh positions and trades data"""
        self.fetch_positions()
        self.fetch_trades()
    
    def fetch_positions(self):
        """Fetch current positions using Neo API client"""
        if not self.auth_handler.is_authenticated():
            return

        try:
            # Get the authenticated client
            client = self.auth_handler.get_client()
            if not client:
                logger.error("No authenticated client available")
                return

            result = client.positions()

            if result and result.get('stat') == 'ok':
                positions_data = result.get('data', [])

                # Process positions data
                processed_positions = []
                total_pnl = 0.0
                
                for pos in positions_data:
                    try:
                        position = {
                            'instrument_token': pos.get('instrumentToken', ''),
                            'trading_symbol': pos.get('tradingSymbol', ''),
                            'product': pos.get('product', ''),
                            'quantity': int(pos.get('netQuantity', 0)),
                            'average_price': float(pos.get('averagePrice', 0)),
                            'last_price': float(pos.get('lastPrice', 0)),
                            'pnl': float(pos.get('unrealizedPnl', 0)),
                            'day_pnl': float(pos.get('dayPnl', 0)),
                            'value': float(pos.get('marketValue', 0)),
                            'exchange': pos.get('exchange', ''),
                            'segment': pos.get('segment', '')
                        }
                        
                        # Calculate additional metrics
                        if position['quantity'] != 0 and position['average_price'] > 0:
                            position['change'] = position['last_price'] - position['average_price']
                            position['change_percent'] = (position['change'] / position['average_price']) * 100
                        else:
                            position['change'] = 0
                            position['change_percent'] = 0
                        
                        processed_positions.append(position)
                        total_pnl += position['pnl']
                        
                    except (ValueError, TypeError) as e:
                        logger.error(f"Error processing position data: {e}")
                        continue
                
                self.positions = processed_positions
                self.daily_pnl = total_pnl
                
                # Emit signals
                self.positions_updated.emit(self.positions)
                self.pnl_updated.emit(self.daily_pnl)
                
                logger.debug(f"Updated {len(self.positions)} positions, Total P&L: ₹{self.daily_pnl:.2f}")

            elif result and result.get('stat') == 'Not_Ok':
                # Handle "Not_Ok" status (might mean no positions)
                logger.info("No positions found")
                return
            else:
                logger.error(f"Unexpected positions API response: {result}")
                return
                
        except Exception as e:
            logger.error(f"Error fetching positions: {e}")
    
    def fetch_trades(self):
        """Fetch today's trades using Neo API client"""
        if not self.auth_handler.is_authenticated():
            return

        try:
            # Get the authenticated client
            client = self.auth_handler.get_client()
            if not client:
                logger.error("No authenticated client available")
                return

            result = client.order_report()

            if result and result.get('stat') == 'ok':
                trades_data = result.get('data', [])

                # Process trades data
                processed_trades = []
                
                for trade in trades_data:
                    try:
                        trade_info = {
                            'trade_id': trade.get('tradeId', ''),
                            'order_id': trade.get('orderId', ''),
                            'instrument_token': trade.get('instrumentToken', ''),
                            'trading_symbol': trade.get('tradingSymbol', ''),
                            'transaction_type': trade.get('transactionType', ''),
                            'quantity': int(trade.get('quantity', 0)),
                            'price': float(trade.get('price', 0)),
                            'value': float(trade.get('value', 0)),
                            'exchange': trade.get('exchange', ''),
                            'product': trade.get('product', ''),
                            'order_type': trade.get('orderType', ''),
                            'trade_time': self._parse_trade_time(trade.get('tradeTime', '')),
                            'brokerage': float(trade.get('brokerage', 0)),
                            'taxes': float(trade.get('taxes', 0))
                        }
                        
                        processed_trades.append(trade_info)
                        
                    except (ValueError, TypeError) as e:
                        logger.error(f"Error processing trade data: {e}")
                        continue
                
                # Sort trades by time (newest first)
                processed_trades.sort(key=lambda x: x['trade_time'], reverse=True)
                
                self.trades = processed_trades
                self.trades_updated.emit(self.trades)
                
                logger.debug(f"Updated {len(self.trades)} trades")

            elif result and result.get('stat') == 'Not_Ok':
                # Handle "Not_Ok" status (might mean no trades today)
                logger.info("No trades found for today")
                return
            else:
                logger.error(f"Unexpected trades API response: {result}")
                return

        except Exception as e:
            logger.error(f"Error fetching trades: {e}")
    
    def _parse_trade_time(self, time_str):
        """Parse trade time string to datetime object"""
        try:
            # Handle different time formats that might be returned
            formats = [
                "%Y-%m-%d %H:%M:%S",
                "%d-%m-%Y %H:%M:%S",
                "%Y-%m-%dT%H:%M:%S",
                "%Y-%m-%dT%H:%M:%S.%f"
            ]
            
            for fmt in formats:
                try:
                    return datetime.strptime(time_str, fmt)
                except ValueError:
                    continue
            
            # If no format matches, return current time
            logger.warning(f"Could not parse trade time: {time_str}")
            return datetime.now()
            
        except Exception:
            return datetime.now()
    
    def get_position_by_token(self, instrument_token):
        """Get position data for specific instrument token"""
        for position in self.positions:
            if position['instrument_token'] == instrument_token:
                return position
        return None
    
    def get_trades_by_symbol(self, trading_symbol):
        """Get trades for specific trading symbol"""
        return [trade for trade in self.trades if trade['trading_symbol'] == trading_symbol]
    
    def get_daily_summary(self):
        """Get daily trading summary"""
        if not self.trades:
            return {
                'total_trades': 0,
                'buy_trades': 0,
                'sell_trades': 0,
                'total_quantity': 0,
                'total_value': 0,
                'total_brokerage': 0,
                'total_taxes': 0,
                'net_pnl': self.daily_pnl
            }
        
        buy_trades = [t for t in self.trades if t['transaction_type'] == 'BUY']
        sell_trades = [t for t in self.trades if t['transaction_type'] == 'SELL']
        
        summary = {
            'total_trades': len(self.trades),
            'buy_trades': len(buy_trades),
            'sell_trades': len(sell_trades),
            'total_quantity': sum(t['quantity'] for t in self.trades),
            'total_value': sum(t['value'] for t in self.trades),
            'total_brokerage': sum(t['brokerage'] for t in self.trades),
            'total_taxes': sum(t['taxes'] for t in self.trades),
            'net_pnl': self.daily_pnl
        }
        
        return summary
    
    def calculate_realized_pnl(self):
        """Calculate realized P&L from completed trades"""
        # Group trades by symbol to match buy/sell pairs
        symbol_trades = {}
        for trade in self.trades:
            symbol = trade['trading_symbol']
            if symbol not in symbol_trades:
                symbol_trades[symbol] = []
            symbol_trades[symbol].append(trade)
        
        total_realized_pnl = 0.0
        
        for symbol, trades in symbol_trades.items():
            # Sort by time
            trades.sort(key=lambda x: x['trade_time'])
            
            position = 0
            avg_price = 0.0
            realized_pnl = 0.0
            
            for trade in trades:
                qty = trade['quantity']
                price = trade['price']
                
                if trade['transaction_type'] == 'BUY':
                    if position == 0:
                        avg_price = price
                    else:
                        avg_price = ((avg_price * position) + (price * qty)) / (position + qty)
                    position += qty
                
                elif trade['transaction_type'] == 'SELL':
                    if position > 0:
                        sell_qty = min(qty, position)
                        realized_pnl += (price - avg_price) * sell_qty
                        position -= sell_qty
            
            total_realized_pnl += realized_pnl
        
        return total_realized_pnl
    
    def export_trades_csv(self, filename=None):
        """Export trades to CSV file"""
        if not filename:
            filename = f"trades_{datetime.now().strftime('%Y%m%d')}.csv"
        
        try:
            import csv
            
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                if not self.trades:
                    logger.warning("No trades to export")
                    return False
                
                fieldnames = list(self.trades[0].keys())
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for trade in self.trades:
                    writer.writerow(trade)
            
            logger.info(f"Trades exported to {filename}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting trades: {e}")
            return False
    
    def get_positions(self):
        """Get current positions list"""
        return self.positions.copy()
    
    def get_trades(self):
        """Get current trades list"""
        return self.trades.copy()
    
    def get_daily_pnl(self):
        """Get current daily P&L"""
        return self.daily_pnl
