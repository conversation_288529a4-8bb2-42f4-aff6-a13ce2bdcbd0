Unlocking Real-Time Option Prices with Kotak Neo API in VS Code: A Detailed Guide
This guide provides a comprehensive procedure for developers and traders to fetch real-time option prices using the Kotak Neo API within the Visual Studio Code (VS Code) environment. By leveraging the WebSocket API, you can establish a persistent connection to receive a continuous stream of live market data, essential for algorithmic trading and real-time analysis.
Prerequisites
Before you begin, ensure you have the following set up:
Kotak Neo Account: A trading and demat account with Kotak Securities.
API Credentials: You'll need your Consumer Key and Consumer Secret, which can be obtained by registering for the Neo Trade API on the Kotak Securities website.[1]
Visual Studio Code: A modern code editor that can be downloaded from its official website.
Python: An installation of Python (version 3.6 or higher) is required.
Git: You need to have Git installed to download the Kotak Neo API client.[2]
Step 1: Setting up Your VS Code Environment
Create a Project Folder: Start by creating a dedicated folder for your project on your local machine.
Open in VS Code: Launch VS Code and open your newly created project folder.
Create a Python File: Inside your project folder, create a new Python file, for instance, main.py.
Install the Kotak Neo API Client: Open the integrated terminal in VS Code (`Ctrl+``) and install the official Python SDK using the following command: [1]
Generated bash
pip install "git+https://github.com/Kotak-Neo/kotak-neo-api.git#egg=neo_api_client"
Use code with caution.
Bash
Step 2: Authentication and Session Generation
This step involves authenticating your credentials and generating a session to interact with the API.
Generated python
from neo_api_client import NeoAPI

# --- 1. Define your credentials ---
consumer_key = "YOUR_CONSUMER_KEY"
consumer_secret = "YOUR_CONSUMER_SECRET"
mobile_number = "YOUR_MOBILE_NUMBER"
password = "YOUR_PASSWORD"

# --- 2. Initialize the API client ---
client = NeoAPI(consumer_key=consumer_key, consumer_secret=consumer_secret, environment='prod')

# --- 3. Login and Generate OTP ---
client.login(mobilenumber=mobile_number, password=password)

# --- 4. Complete 2FA Authentication ---
otp = input("Enter the OTP received on your mobile: ")
client.session_2fa(OTP=otp)

print("Successfully logged in!")
Use code with caution.
Python
Note: The environment parameter can be set to 'uat' for the user acceptance testing environment or 'prod' for the live production environment. [1]
Step 3: Finding the Instrument Token for Options
To get live data for a specific option, you need its unique instrument_token. You can obtain this by downloading and searching the scrip master file provided by the API.
Download the Scrip Master: Use the scrip_master() method to get the file paths for various exchange segments. You can then download and read these CSV files. [1]
Generated python
import pandas as pd
from io import StringIO
import urllib.request

# Get the scrip master file paths
file_paths = client.scrip_master()

# We are interested in the NFO (NSE F&O) segment for options
nfo_path = [path for path in file_paths if 'nfo' in path.lower()]

# Download and read the NFO scrip master into a pandas DataFrame
response = urllib.request.urlopen(nfo_path)
nfo_df = pd.read_csv(StringIO(response.read().decode('utf-8')))
Use code with caution.
Python
Search for the Instrument Token: Now you can filter the DataFrame to find the token for your desired option contract. For example, to find the token for a NIFTY call option:
Generated python
underlying_symbol = "NIFTY"
expiry_date = "YYYY-MM-DD"  # Replace with the desired expiry date
strike_price = 23500  # Replace with the desired strike price
option_type = "CE"  # "CE" for Call, "PE" for Put

# Filter the DataFrame to find the specific option contract
option_contract = nfo_df[
    (nfo_df['pTrdSymbol'].str.contains(underlying_symbol, case=False)) &
    (nfo_df['pExpiryDate'] == expiry_date) &
    (nfo_df['pStrikePrice'] == strike_price) &
    (nfo_df['pOptionType'] == option_type)
]

if not option_contract.empty:
    instrument_token = option_contract.iloc['pSymbol']
    print(f"Instrument Token for {underlying_symbol} {strike_price} {option_type} expiring on {expiry_date} is: {instrument_token}")
else:
    print("Option contract not found.")
Use code with caution.
Python
You can also use the search_scrip method for a more direct search. [17]
Step 4: Fetching Real-Time Prices with WebSocket
The WebSocket provides a continuous stream of live data without needing to send repeated requests. [1]
Define Callback Functions: These functions will handle events from the WebSocket connection, such as receiving a message or an error. [1, 2]
Generated python
def on_message(message):
    """Callback for when a message is received."""
    print(f"Live Data: {message}")

def on_error(error_message):
    """Callback for when an error occurs."""
    print(f"Error: {error_message}")

def on_close(message):
    """Callback for when the connection is closed."""
    print("Connection closed")

def on_open(message):
    """Callback for when the connection is successfully opened."""
    print("Connection opened")

# Assign the callbacks to the client
client.on_message = on_message
client.on_error = on_error
client.on_close = on_close
client.on_open = on_open
Use code with caution.
Python
Subscribe to Instrument Tokens: Once you have the instrument token(s), you can subscribe to receive their live feed. [17]
Generated python
# Use the instrument_token found in the previous step
instrument_tokens = [{"instrument_token": str(instrument_token), "exchange_segment": "NFO"}]

# Subscribe to the live feed
client.subscribe(instrument_tokens=instrument_tokens)
Use code with caution.
Python
Your on_message function will now start receiving a stream of JSON objects containing the live price data for the subscribed option contract. You can parse this JSON to extract the "Last Traded Price" (ltp) and other relevant information.
Step 5: Unsubscribing from the Feed
When you no longer need the live data for a particular instrument, you can unsubscribe. [17]
Generated python
client.un_subscribe(instrument_tokens=instrument_tokens)
Use code with caution.
Python
By following this detailed procedure, you can successfully set up your VS Code environment to connect to the Kotak Neo API and fetch real-time prices for options, enabling you to build sophisticated trading applications and analysis tools. For further details and advanced functionalities, refer to the official Kotak Neo API documentation on their GitHub page. [4]
Sources
help
algotest.in
tradenvesteasy.com