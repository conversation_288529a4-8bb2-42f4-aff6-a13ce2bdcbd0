@echo off
title Kotak Neo Trading Bot
echo Starting Kotak Neo Trading Bot...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.7 or higher from https://python.org
    pause
    exit /b 1
)

REM Change to script directory
cd /d "%~dp0"

REM Check if main.py exists
if not exist "main.py" (
    echo Error: main.py not found in current directory
    echo Please ensure all application files are present
    pause
    exit /b 1
)

REM Run the application
python main.py

REM Keep window open if there was an error
if errorlevel 1 (
    echo.
    echo Application exited with error code %errorlevel%
    pause
)
