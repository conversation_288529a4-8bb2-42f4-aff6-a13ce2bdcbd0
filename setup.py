# setup.py - Setup script for the Kotak Neo Trading Bot

import os
import sys
import subprocess
import json

def install_dependencies():
    """Install required Python packages"""
    print("Installing required dependencies...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Failed to install dependencies: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    directories = [
        "logs",
        "data",
        "exports"
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"Created directory: {directory}")

def setup_config():
    """Setup initial configuration"""
    config_file = "user_config.json"
    
    if os.path.exists(config_file):
        print("Configuration file already exists.")
        return
    
    print("\nSetting up initial configuration...")
    
    # Get user inputs
    neo_fin_key = input("Enter your Kotak Neo Fin Key (from API credentials): ").strip()
    mobile_number = input("Enter your registered mobile number: ").strip()
    
    # Load default config
    with open(config_file, 'r') as f:
        config = json.load(f)
    
    # Update with user inputs
    config['neo_fin_key'] = neo_fin_key
    config['mobile_number'] = mobile_number
    
    # Save updated config
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    print("Configuration saved successfully!")

def create_desktop_shortcut():
    """Create desktop shortcut (Windows)"""
    if sys.platform != "win32":
        return
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        path = os.path.join(desktop, "Kotak Neo Trading Bot.lnk")
        target = os.path.join(os.getcwd(), "main.py")
        wDir = os.getcwd()
        icon = target
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        shortcut.Targetpath = sys.executable
        shortcut.Arguments = f'"{target}"'
        shortcut.WorkingDirectory = wDir
        shortcut.IconLocation = icon
        shortcut.save()
        
        print("Desktop shortcut created!")
        
    except ImportError:
        print("Could not create desktop shortcut (winshell not available)")
    except Exception as e:
        print(f"Could not create desktop shortcut: {e}")

def main():
    """Main setup function"""
    print("=" * 50)
    print("Kotak Neo Trading Bot Setup")
    print("=" * 50)
    
    # Check Python version
    if sys.version_info < (3, 7):
        print("Error: Python 3.7 or higher is required")
        return False
    
    print(f"Python version: {sys.version}")
    
    # Install dependencies
    if not install_dependencies():
        return False
    
    # Create directories
    create_directories()
    
    # Setup configuration
    setup_config()
    
    # Create desktop shortcut
    create_desktop_shortcut()
    
    print("\n" + "=" * 50)
    print("Setup completed successfully!")
    print("=" * 50)
    print("\nNext steps:")
    print("1. Make sure you have valid Kotak Neo API credentials")
    print("2. Update user_config.json with your neo_fin_key if not done already")
    print("3. Run the application using: python main.py")
    print("\nFor help and documentation, see README.md")
    
    return True

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
