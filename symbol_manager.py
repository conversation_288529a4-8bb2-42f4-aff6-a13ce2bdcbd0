# symbol_manager.py - Symbol and instrument management for Kotak Neo API

import requests
import pandas as pd
import os
from datetime import datetime, timedelta
import config
from logger import logger

class SymbolManager:
    """Manages instrument symbols and tokens for options trading"""
    
    def __init__(self, auth_handler):
        self.auth_handler = auth_handler
        self.instruments_df = None
        self.watchlist = []
        self.last_update = None
        self.load_instruments()
    
    def download_instruments(self):
        """Download instrument master file from Kotak Neo"""
        try:
            logger.info("Downloading instrument master file...")
            
            # Use the scrip master endpoint
            url = f"{config.BASE_URL}/v3/scrips/master"
            params = {"exchange_segment": "nse_fo"}  # NSE F&O for options
            
            response = requests.get(
                url, 
                headers=config.API_HEADERS, 
                params=params,
                timeout=30
            )
            response.raise_for_status()
            
            # Save the file
            with open(config.INSTRUMENTS_FILE, 'w', encoding='utf-8') as f:
                f.write(response.text)
            
            self.last_update = datetime.now()
            logger.info("Instrument master file downloaded successfully")
            return True
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to download instruments: {e}")
            return False
        except Exception as e:
            logger.error(f"Error downloading instruments: {e}")
            return False
    
    def load_instruments(self):
        """Load instruments from file or download if needed"""
        # Check if file exists and is recent (less than 1 day old)
        if os.path.exists(config.INSTRUMENTS_FILE):
            file_age = datetime.now() - datetime.fromtimestamp(
                os.path.getmtime(config.INSTRUMENTS_FILE)
            )
            if file_age < timedelta(days=1):
                try:
                    self.instruments_df = pd.read_csv(config.INSTRUMENTS_FILE)
                    logger.info(f"Loaded {len(self.instruments_df)} instruments from file")
                    return True
                except Exception as e:
                    logger.error(f"Error loading instruments file: {e}")
        
        # Download fresh data
        if self.auth_handler.is_authenticated():
            if self.download_instruments():
                try:
                    self.instruments_df = pd.read_csv(config.INSTRUMENTS_FILE)
                    logger.info(f"Loaded {len(self.instruments_df)} instruments")
                    return True
                except Exception as e:
                    logger.error(f"Error loading downloaded instruments: {e}")
        
        logger.warning("Could not load instruments - some features may not work")
        return False
    
    def find_instrument_token(self, symbol, expiry_date, strike_price, option_type):
        """Find instrument token for given option parameters"""
        if self.instruments_df is None:
            logger.error("Instruments not loaded")
            return None
        
        try:
            # Convert expiry date to the format used in the file
            if isinstance(expiry_date, str):
                # Convert from "10-JUL-2025" to "10JUL25" format
                date_obj = datetime.strptime(expiry_date, "%d-%b-%Y")
                formatted_expiry = date_obj.strftime("%d%b%y").upper()
            else:
                formatted_expiry = expiry_date
            
            # Create the search pattern
            # Format: NIFTY10JUL2525500.00PE
            search_pattern = f"{symbol}{formatted_expiry}{strike_price}.00{option_type}"
            
            # Search in the dataframe
            # Assuming columns: pScripRefKey, pTrdSymbol, pSymbol (instrument token)
            matches = self.instruments_df[
                self.instruments_df['pScripRefKey'].str.contains(search_pattern, na=False)
            ]
            
            if not matches.empty:
                instrument_token = matches.iloc[0]['pSymbol']
                trading_symbol = matches.iloc[0]['pTrdSymbol']
                logger.info(f"Found instrument: {trading_symbol} -> Token: {instrument_token}")
                return {
                    'instrument_token': str(instrument_token),
                    'trading_symbol': trading_symbol,
                    'search_key': search_pattern
                }
            else:
                # Try alternative search by individual components
                symbol_matches = self.instruments_df[
                    (self.instruments_df['pSymbolName'] == symbol) &
                    (self.instruments_df['pExpiryDate'].str.contains(formatted_expiry, na=False)) &
                    (self.instruments_df['pStrikePrice'] == float(strike_price)) &
                    (self.instruments_df['pOptionType'] == option_type)
                ]
                
                if not symbol_matches.empty:
                    instrument_token = symbol_matches.iloc[0]['pSymbol']
                    trading_symbol = symbol_matches.iloc[0]['pTrdSymbol']
                    logger.info(f"Found instrument (alt search): {trading_symbol} -> Token: {instrument_token}")
                    return {
                        'instrument_token': str(instrument_token),
                        'trading_symbol': trading_symbol,
                        'search_key': search_pattern
                    }
                
                logger.warning(f"Instrument not found for: {search_pattern}")
                return None
                
        except Exception as e:
            logger.error(f"Error finding instrument token: {e}")
            return None
    
    def add_to_watchlist(self, symbol_info):
        """Add symbol to watchlist"""
        if symbol_info and symbol_info not in self.watchlist:
            self.watchlist.append(symbol_info)
            logger.info(f"Added to watchlist: {symbol_info['trading_symbol']}")
            return True
        return False
    
    def remove_from_watchlist(self, instrument_token):
        """Remove symbol from watchlist by instrument token"""
        for i, symbol_info in enumerate(self.watchlist):
            if symbol_info['instrument_token'] == instrument_token:
                removed = self.watchlist.pop(i)
                logger.info(f"Removed from watchlist: {removed['trading_symbol']}")
                return True
        return False
    
    def get_watchlist(self):
        """Get current watchlist"""
        return self.watchlist.copy()
    
    def get_watchlist_tokens(self):
        """Get list of instrument tokens from watchlist"""
        return [symbol['instrument_token'] for symbol in self.watchlist]
    
    def get_symbol_by_token(self, instrument_token):
        """Get symbol info by instrument token"""
        for symbol_info in self.watchlist:
            if symbol_info['instrument_token'] == instrument_token:
                return symbol_info
        return None
    
    def search_symbols(self, search_term, limit=10):
        """Search for symbols matching the search term"""
        if self.instruments_df is None:
            return []
        
        try:
            # Search in trading symbol and symbol name
            matches = self.instruments_df[
                (self.instruments_df['pTrdSymbol'].str.contains(search_term, case=False, na=False)) |
                (self.instruments_df['pSymbolName'].str.contains(search_term, case=False, na=False))
            ].head(limit)
            
            results = []
            for _, row in matches.iterrows():
                results.append({
                    'instrument_token': str(row['pSymbol']),
                    'trading_symbol': row['pTrdSymbol'],
                    'symbol_name': row['pSymbolName'],
                    'expiry': row.get('pExpiryDate', ''),
                    'strike': row.get('pStrikePrice', ''),
                    'option_type': row.get('pOptionType', '')
                })
            
            return results
            
        except Exception as e:
            logger.error(f"Error searching symbols: {e}")
            return []
    
    def get_default_symbol(self):
        """Get the default trading symbol"""
        default = config.DEFAULT_SYMBOL
        return self.find_instrument_token(
            default['instrument'],
            default['expiry'],
            default['strike'],
            default['type']
        )
    
    def refresh_instruments(self):
        """Force refresh of instrument data"""
        if self.auth_handler.is_authenticated():
            if self.download_instruments():
                return self.load_instruments()
        return False
