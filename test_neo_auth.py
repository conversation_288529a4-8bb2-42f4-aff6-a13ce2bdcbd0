#!/usr/bin/env python3
# test_neo_auth.py - Test script for Neo API authentication

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from neo_auth_handler import NeoAuthHandler
import config

def test_authentication():
    """Test the Neo API authentication flow"""
    print("=" * 60)
    print("Testing Kotak Neo API Authentication")
    print("=" * 60)
    
    # Initialize auth handler
    auth = NeoAuthHandler()
    
    if not auth.client:
        print("❌ Failed to initialize Neo API client")
        print("Check your consumer_key and consumer_secret in user_config.json")
        return False
    
    print("✅ Neo API client initialized successfully")
    
    # Get mobile number from config
    mobile_number = config.config.get('mobile_number', '')
    if not mobile_number:
        print("❌ Mobile number not found in configuration")
        return False
    
    print(f"📱 Using mobile number: {mobile_number}")
    
    # Test OTP request
    print("\n" + "-" * 40)
    print("Testing OTP Request")
    print("-" * 40)
    
    success, message = auth.request_otp(mobile_number)
    
    if success:
        print(f"✅ {message}")
        print("\n📲 Please check your mobile for OTP")
        
        # Get OTP from user
        try:
            otp = input("\nEnter OTP: ").strip()
            
            if otp:
                print("\n" + "-" * 40)
                print("Testing OTP Validation")
                print("-" * 40)
                
                success, message = auth.validate_otp(otp)
                
                if success:
                    print(f"✅ {message}")
                    
                    # Test connection verification
                    print("\n" + "-" * 40)
                    print("Testing Connection Verification")
                    print("-" * 40)
                    
                    success, message = auth.verify_connection()
                    
                    if success:
                        print(f"✅ {message}")
                        print("\n🎉 All tests passed! Authentication is working correctly.")
                        return True
                    else:
                        print(f"❌ Connection verification failed: {message}")
                        return False
                else:
                    print(f"❌ OTP validation failed: {message}")
                    return False
            else:
                print("❌ No OTP entered")
                return False
                
        except KeyboardInterrupt:
            print("\n❌ Test cancelled by user")
            return False
        except Exception as e:
            print(f"❌ Error during OTP input: {e}")
            return False
    else:
        print(f"❌ OTP request failed: {message}")
        return False

def main():
    """Main test function"""
    print("🔍 Kotak Neo API Authentication Test")
    
    # Check configuration
    print("\nChecking configuration...")
    
    consumer_key = config.config.get('consumer_key', '')
    consumer_secret = config.config.get('consumer_secret', '')
    neo_fin_key = config.config.get('neo_fin_key', '')
    mobile_number = config.config.get('mobile_number', '')
    
    print(f"Consumer Key: {'✅ Set' if consumer_key else '❌ Missing'}")
    print(f"Consumer Secret: {'✅ Set' if consumer_secret else '❌ Missing'}")
    print(f"Neo Fin Key: {'✅ Set' if neo_fin_key else '❌ Missing'}")
    print(f"Mobile Number: {'✅ Set' if mobile_number else '❌ Missing'}")
    
    if not all([consumer_key, consumer_secret, mobile_number]):
        print("\n❌ Missing required configuration!")
        print("Please update user_config.json with your credentials")
        return False
    
    # Run authentication test
    success = test_authentication()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 SUCCESS: Authentication test completed successfully!")
        print("Your Kotak Neo API credentials are working correctly.")
    else:
        print("❌ FAILED: Authentication test failed!")
        print("Please check your credentials and try again.")
    print("=" * 60)
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
