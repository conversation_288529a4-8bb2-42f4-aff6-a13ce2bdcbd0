#!/usr/bin/env python3
# test_sdk_simple.py - Simple test of Kotak Neo SDK based on official examples

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from neo_api_client import NeoAPI
import config

def test_sdk_login():
    """Test the SDK login flow exactly as shown in official documentation"""
    print("=" * 60)
    print("Testing Kotak Neo SDK Login Flow")
    print("=" * 60)
    
    # Get credentials
    consumer_key = config.config.get('consumer_key', '')
    consumer_secret = config.config.get('consumer_secret', '')
    neo_fin_key = config.config.get('neo_fin_key', '')
    mobile_number = config.config.get('mobile_number', '')
    trading_password = config.config.get('trading_password', '')
    
    print(f"Consumer Key: {consumer_key[:10]}..." if consumer_key else "❌ Missing")
    print(f"Consumer Secret: {consumer_secret[:10]}..." if consumer_secret else "❌ Missing")
    print(f"Neo Fin Key: {neo_fin_key}")
    print(f"Mobile Number: {mobile_number}")
    print(f"Trading Password: {'✅ Set' if trading_password else '❌ Missing'}")
    
    if not all([consumer_key, consumer_secret, mobile_number, trading_password]):
        print("\n❌ Missing required credentials!")
        return False
    
    try:
        print("\n" + "-" * 40)
        print("Step 1: Initialize Neo API Client")
        print("-" * 40)
        
        # Initialize client exactly as shown in official docs
        client = NeoAPI(
            consumer_key=consumer_key,
            consumer_secret=consumer_secret,
            environment='prod',  # Use 'uat' for testing, 'prod' for live
            neo_fin_key=neo_fin_key
        )
        
        print("✅ Neo API client initialized")
        
        print("\n" + "-" * 40)
        print("Step 2: Initiate Login (sends OTP)")
        print("-" * 40)
        
        # This should send OTP to mobile
        login_result = client.login(mobilenumber=mobile_number, password=trading_password)
        
        print(f"Login result: {login_result}")
        
        if login_result:
            print("✅ Login initiated - OTP should be sent to your mobile")
            print("📱 Please check your mobile for OTP")
            
            # Get OTP from user
            try:
                otp = input("\nEnter OTP: ").strip()
                
                if otp:
                    print("\n" + "-" * 40)
                    print("Step 3: Complete 2FA with OTP")
                    print("-" * 40)
                    
                    # Complete 2FA
                    session_result = client.session_2fa(OTP=otp)
                    
                    print(f"Session 2FA result: {session_result}")
                    
                    if session_result:
                        print("✅ 2FA completed successfully!")
                        
                        # Test a simple API call
                        print("\n" + "-" * 40)
                        print("Step 4: Test API Call")
                        print("-" * 40)
                        
                        try:
                            positions = client.positions()
                            print("✅ API call successful!")
                            print(f"Positions result: {positions}")
                            return True
                        except Exception as e:
                            print(f"⚠️ API call failed: {e}")
                            return True  # Login was successful even if positions failed
                    else:
                        print("❌ 2FA failed - invalid OTP")
                        return False
                else:
                    print("❌ No OTP entered")
                    return False
                    
            except KeyboardInterrupt:
                print("\n❌ Test cancelled")
                return False
        else:
            print("❌ Login initiation failed")
            return False
            
    except Exception as e:
        print(f"❌ Error during SDK test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🔍 Kotak Neo SDK Simple Test")
    
    success = test_sdk_login()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 SUCCESS: SDK login test completed!")
    else:
        print("❌ FAILED: SDK login test failed!")
    print("=" * 60)
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
