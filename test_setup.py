#!/usr/bin/env python3
# test_setup.py - Test script to validate the trading bot setup

import sys
import os
import json
import traceback
from datetime import datetime

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    modules_to_test = [
        ('requests', 'HTTP requests'),
        ('websocket', 'WebSocket client'),
        ('apscheduler', 'Task scheduler'),
        ('PyQt5.QtWidgets', 'PyQt5 GUI framework'),
        ('pandas', 'Data manipulation'),
        ('numpy', 'Numerical computing')
    ]
    
    failed_imports = []
    
    for module_name, description in modules_to_test:
        try:
            __import__(module_name)
            print(f"  ✓ {module_name} ({description})")
        except ImportError as e:
            print(f"  ✗ {module_name} ({description}) - {e}")
            failed_imports.append(module_name)
    
    if failed_imports:
        print(f"\nFailed to import: {', '.join(failed_imports)}")
        print("Run: pip install -r requirements.txt")
        return False
    
    print("All imports successful!")
    return True

def test_application_modules():
    """Test if application modules can be imported"""
    print("\nTesting application modules...")
    
    app_modules = [
        'config',
        'logger',
        'auth_handler',
        'symbol_manager',
        'websocket_handler',
        'order_manager',
        'portfolio_manager',
        'main_window',
        'trading_app'
    ]
    
    failed_modules = []
    
    for module_name in app_modules:
        try:
            __import__(module_name)
            print(f"  ✓ {module_name}")
        except ImportError as e:
            print(f"  ✗ {module_name} - {e}")
            failed_modules.append(module_name)
        except Exception as e:
            print(f"  ⚠ {module_name} - {e}")
    
    if failed_modules:
        print(f"\nFailed to import application modules: {', '.join(failed_modules)}")
        return False
    
    print("All application modules imported successfully!")
    return True

def test_configuration():
    """Test configuration file"""
    print("\nTesting configuration...")
    
    config_file = "user_config.json"
    
    if not os.path.exists(config_file):
        print(f"  ✗ Configuration file {config_file} not found")
        return False
    
    try:
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        print(f"  ✓ Configuration file loaded")
        
        # Check required fields
        required_fields = ['neo_fin_key', 'mobile_number']
        missing_fields = []
        
        for field in required_fields:
            if not config.get(field):
                missing_fields.append(field)
        
        if missing_fields:
            print(f"  ⚠ Missing configuration: {', '.join(missing_fields)}")
            print("    Please update user_config.json with your API credentials")
        else:
            print("  ✓ All required configuration present")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"  ✗ Invalid JSON in configuration file: {e}")
        return False
    except Exception as e:
        print(f"  ✗ Error reading configuration: {e}")
        return False

def test_file_structure():
    """Test if all required files are present"""
    print("\nTesting file structure...")
    
    required_files = [
        'main.py',
        'trading_app.py',
        'config.py',
        'logger.py',
        'auth_handler.py',
        'symbol_manager.py',
        'websocket_handler.py',
        'order_manager.py',
        'portfolio_manager.py',
        'main_window.py',
        'requirements.txt',
        'user_config.json',
        'README.md'
    ]
    
    missing_files = []
    
    for file_name in required_files:
        if os.path.exists(file_name):
            print(f"  ✓ {file_name}")
        else:
            print(f"  ✗ {file_name}")
            missing_files.append(file_name)
    
    if missing_files:
        print(f"\nMissing files: {', '.join(missing_files)}")
        return False
    
    print("All required files present!")
    return True

def test_directories():
    """Test if directories can be created"""
    print("\nTesting directory creation...")
    
    test_dirs = ['logs', 'data', 'exports']
    
    for dir_name in test_dirs:
        try:
            if not os.path.exists(dir_name):
                os.makedirs(dir_name)
                print(f"  ✓ Created directory: {dir_name}")
            else:
                print(f"  ✓ Directory exists: {dir_name}")
        except Exception as e:
            print(f"  ✗ Failed to create directory {dir_name}: {e}")
            return False
    
    return True

def test_logging():
    """Test logging functionality"""
    print("\nTesting logging...")
    
    try:
        from logger import logger
        
        # Test log message
        test_message = f"Test log message at {datetime.now()}"
        logger.info(test_message)
        
        # Check if log file was created
        log_file = "trading_bot.log"
        if os.path.exists(log_file):
            print(f"  ✓ Log file created: {log_file}")
            
            # Check if message was written
            with open(log_file, 'r') as f:
                content = f.read()
                if test_message in content:
                    print("  ✓ Log message written successfully")
                else:
                    print("  ⚠ Log message not found in file")
        else:
            print("  ⚠ Log file not created")
        
        return True
        
    except Exception as e:
        print(f"  ✗ Logging test failed: {e}")
        return False

def test_gui():
    """Test GUI components"""
    print("\nTesting GUI components...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        
        # Create QApplication instance
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        print("  ✓ QApplication created")
        
        # Test importing main window
        from main_window import LoginDialog, SymbolManagerWidget, TradingControlWidget
        print("  ✓ Main window components imported")
        
        # Test creating login dialog
        dialog = LoginDialog()
        print("  ✓ Login dialog created")
        
        return True
        
    except Exception as e:
        print(f"  ✗ GUI test failed: {e}")
        traceback.print_exc()
        return False

def run_all_tests():
    """Run all tests"""
    print("=" * 60)
    print("Kotak Neo Trading Bot - Setup Validation")
    print("=" * 60)
    print(f"Python version: {sys.version}")
    print(f"Platform: {sys.platform}")
    print(f"Working directory: {os.getcwd()}")
    print()
    
    tests = [
        ("File Structure", test_file_structure),
        ("Python Imports", test_imports),
        ("Application Modules", test_application_modules),
        ("Configuration", test_configuration),
        ("Directories", test_directories),
        ("Logging", test_logging),
        ("GUI Components", test_gui)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'-' * 40}")
        print(f"Running: {test_name}")
        print(f"{'-' * 40}")
        
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} PASSED")
            else:
                print(f"✗ {test_name} FAILED")
        except Exception as e:
            print(f"✗ {test_name} ERROR: {e}")
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All tests passed! The trading bot is ready to use.")
        print("\nNext steps:")
        print("1. Update user_config.json with your Kotak Neo API credentials")
        print("2. Run the application: python main.py")
    else:
        print("❌ Some tests failed. Please fix the issues before running the application.")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    print("\nPress Enter to exit...")
    input()
    sys.exit(0 if success else 1)
