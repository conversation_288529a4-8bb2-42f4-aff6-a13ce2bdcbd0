# trading_app.py - Main trading application

import sys
import os
from PyQt5.QtWidgets import QA<PERSON><PERSON>, QMainWindow, QTabWidget, QVBoxLayout, QWidget, QStatusBar, QMenuBar, QAction, QMessageBox, Q<PERSON>plitter
from PyQt5.QtCore import Qt, QTimer, pyqtSlot
from PyQt5.QtGui import QIcon, QPalette, QColor

# Import our modules
import config
from logger import logger
from neo_auth_handler import <PERSON>AuthHandler
from symbol_manager import SymbolManager
from websocket_handler import WebSocketClient
from order_manager import OrderManager
from portfolio_manager import PortfolioManager
from main_window import (LoginDialog, SymbolManagerWidget, TradingControlWidget, 
                        PositionsWidget, TradesWidget, LogWidget)

class TradingApplication(QMainWindow):
    """Main trading application window"""
    
    def __init__(self):
        super().__init__()
        
        # Initialize components
        self.auth_handler = NeoAuthHandler()
        self.symbol_manager = SymbolManager(self.auth_handler)
        self.websocket_client = WebSocketClient(self.auth_handler)
        self.order_manager = OrderManager(self.auth_handler, self.symbol_manager)
        self.portfolio_manager = PortfolioManager(self.auth_handler)
        
        # UI components
        self.symbol_widget = None
        self.trading_widget = None
        self.positions_widget = None
        self.trades_widget = None
        self.log_widget = None
        
        # Connection status
        self.is_connected = False
        
        self.setup_ui()
        self.setup_connections()
        self.apply_dark_theme()
        
        # Auto-login if session exists
        if self.auth_handler.is_authenticated():
            self.on_login_success()
        else:
            self.show_login_dialog()
    
    def setup_ui(self):
        """Setup the main UI"""
        self.setWindowTitle("Kotak Neo Trading Bot")
        self.setGeometry(100, 100, config.WINDOW_WIDTH, config.WINDOW_HEIGHT)
        
        # Create menu bar
        self.create_menu_bar()
        
        # Create status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Disconnected")
        
        # Create main widget and layout
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        
        # Create splitter for main layout
        main_splitter = QSplitter(Qt.Vertical)
        
        # Top section - tabs
        self.tab_widget = QTabWidget()
        
        # Symbol management tab
        self.symbol_widget = SymbolManagerWidget(self.symbol_manager, self.websocket_client)
        self.tab_widget.addTab(self.symbol_widget, "Symbols & Watchlist")
        
        # Trading controls tab
        self.trading_widget = TradingControlWidget(self.order_manager)
        self.tab_widget.addTab(self.trading_widget, "Trading Controls")
        
        # Positions tab
        self.positions_widget = PositionsWidget()
        self.tab_widget.addTab(self.positions_widget, "Positions")
        
        # Trades tab
        self.trades_widget = TradesWidget()
        self.tab_widget.addTab(self.trades_widget, "Trades")
        
        main_splitter.addWidget(self.tab_widget)
        
        # Bottom section - logs
        self.log_widget = LogWidget()
        main_splitter.addWidget(self.log_widget)
        
        # Set splitter proportions
        main_splitter.setSizes([600, 200])
        
        # Set main layout
        layout = QVBoxLayout()
        layout.addWidget(main_splitter)
        main_widget.setLayout(layout)
        
        logger.info("UI setup completed")
    
    def create_menu_bar(self):
        """Create application menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu('File')
        
        login_action = QAction('Login', self)
        login_action.triggered.connect(self.show_login_dialog)
        file_menu.addAction(login_action)
        
        logout_action = QAction('Logout', self)
        logout_action.triggered.connect(self.logout)
        file_menu.addAction(logout_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction('Exit', self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Tools menu
        tools_menu = menubar.addMenu('Tools')
        
        refresh_instruments_action = QAction('Refresh Instruments', self)
        refresh_instruments_action.triggered.connect(self.refresh_instruments)
        tools_menu.addAction(refresh_instruments_action)
        
        verify_connection_action = QAction('Verify Connection', self)
        verify_connection_action.triggered.connect(self.verify_connection)
        tools_menu.addAction(verify_connection_action)
        
        # Help menu
        help_menu = menubar.addMenu('Help')
        
        about_action = QAction('About', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_connections(self):
        """Setup signal connections between components"""
        # WebSocket connections
        self.websocket_client.tick_received.connect(self.on_tick_received)
        self.websocket_client.connection_status.connect(self.on_connection_status)
        self.websocket_client.error_occurred.connect(self.on_websocket_error)
        
        # Order manager connections
        self.order_manager.order_placed.connect(self.on_order_placed)
        self.order_manager.order_executed.connect(self.on_order_executed)
        self.order_manager.trade_completed.connect(self.on_trade_completed)
        self.order_manager.error_occurred.connect(self.on_order_error)
        
        # Portfolio manager connections
        self.portfolio_manager.positions_updated.connect(self.positions_widget.update_positions)
        self.portfolio_manager.trades_updated.connect(self.trades_widget.update_trades)
        self.portfolio_manager.pnl_updated.connect(self.positions_widget.update_pnl)
        self.portfolio_manager.error_occurred.connect(self.on_portfolio_error)
        
        # Logger connection
        qt_handler = logger.get_qt_handler()
        qt_handler.log_signal.connect(self.log_widget.add_log_message)
        
        logger.info("Signal connections setup completed")
    
    def apply_dark_theme(self):
        """Apply dark theme to the application"""
        # Force dark theme regardless of config
        dark_palette = QPalette()

        # Window colors
        dark_palette.setColor(QPalette.Window, QColor(53, 53, 53))
        dark_palette.setColor(QPalette.WindowText, QColor(255, 255, 255))

        # Base colors (input fields, etc.)
        dark_palette.setColor(QPalette.Base, QColor(25, 25, 25))
        dark_palette.setColor(QPalette.AlternateBase, QColor(53, 53, 53))

        # Text colors
        dark_palette.setColor(QPalette.Text, QColor(255, 255, 255))
        dark_palette.setColor(QPalette.BrightText, QColor(255, 0, 0))

        # Button colors
        dark_palette.setColor(QPalette.Button, QColor(53, 53, 53))
        dark_palette.setColor(QPalette.ButtonText, QColor(255, 255, 255))

        # Tooltip colors
        dark_palette.setColor(QPalette.ToolTipBase, QColor(0, 0, 0))
        dark_palette.setColor(QPalette.ToolTipText, QColor(255, 255, 255))

        # Selection colors
        dark_palette.setColor(QPalette.Highlight, QColor(42, 130, 218))
        dark_palette.setColor(QPalette.HighlightedText, QColor(0, 0, 0))

        # Link colors
        dark_palette.setColor(QPalette.Link, QColor(42, 130, 218))
        dark_palette.setColor(QPalette.LinkVisited, QColor(255, 0, 255))

        # Apply palette to application
        self.setPalette(dark_palette)

        # Also apply to QApplication for global effect
        from PyQt5.QtWidgets import QApplication
        QApplication.instance().setPalette(dark_palette)

        # Set additional stylesheet for better control
        self.setStyleSheet("""
            QMainWindow {
                background-color: #353535;
                color: #ffffff;
            }
            QTabWidget::pane {
                border: 1px solid #555555;
                background-color: #353535;
            }
            QTabBar::tab {
                background-color: #555555;
                color: #ffffff;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: #2a82da;
            }
            QTableWidget {
                background-color: #191919;
                color: #ffffff;
                gridline-color: #555555;
            }
            QHeaderView::section {
                background-color: #555555;
                color: #ffffff;
                padding: 4px;
                border: 1px solid #777777;
            }
            QLineEdit {
                background-color: #191919;
                color: #ffffff;
                border: 1px solid #555555;
                padding: 4px;
            }
            QComboBox {
                background-color: #191919;
                color: #ffffff;
                border: 1px solid #555555;
                padding: 4px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                border: 2px solid #ffffff;
                width: 3px;
                height: 3px;
            }
            QPushButton {
                background-color: #555555;
                color: #ffffff;
                border: 1px solid #777777;
                padding: 6px 12px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #666666;
            }
            QPushButton:pressed {
                background-color: #444444;
            }
            QTextEdit {
                background-color: #191919;
                color: #ffffff;
                border: 1px solid #555555;
            }
            QLabel {
                color: #ffffff;
            }
            QGroupBox {
                color: #ffffff;
                border: 1px solid #555555;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                color: #ffffff;
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
    
    def show_login_dialog(self):
        """Show login dialog"""
        dialog = LoginDialog(self.auth_handler, self)
        if dialog.exec_() == dialog.Accepted:
            self.perform_login(dialog.mobile_number, dialog.otp)
    
    def perform_login(self, mobile_number, otp):
        """Perform login with mobile and OTP"""
        logger.info("Completing login with OTP...")

        # OTP was already requested in the dialog, now validate it
        success, message = self.auth_handler.validate_otp(otp)
        if success:
            self.on_login_success()
            # Save mobile number for future use
            config.config.set('mobile_number', mobile_number)
        else:
            QMessageBox.critical(self, "Login Error", f"Login failed: {message}")
    
    def on_login_success(self):
        """Handle successful login"""
        logger.info("Login successful")
        self.is_connected = True
        self.status_bar.showMessage("Connected")
        
        # Start components
        self.symbol_manager.load_instruments()
        self.websocket_client.connect()
        self.portfolio_manager.start_monitoring()
        
        # Add default symbol to watchlist
        default_symbol = self.symbol_manager.get_default_symbol()
        if default_symbol:
            self.symbol_manager.add_to_watchlist(default_symbol)
            self.symbol_widget.update_watchlist_table()
            
            # Subscribe to WebSocket
            if self.websocket_client.is_connected:
                self.websocket_client.subscribe([default_symbol['instrument_token']])
    
    def logout(self):
        """Logout and cleanup"""
        logger.info("Logging out...")
        
        # Stop components
        self.websocket_client.disconnect()
        self.portfolio_manager.stop_monitoring()
        self.order_manager.cancel_scheduled_orders()
        
        # Clear authentication
        self.auth_handler.logout()
        
        self.is_connected = False
        self.status_bar.showMessage("Disconnected")
        
        QMessageBox.information(self, "Logout", "Logged out successfully")
    
    def refresh_instruments(self):
        """Refresh instrument master data"""
        if self.auth_handler.is_authenticated():
            success = self.symbol_manager.refresh_instruments()
            if success:
                QMessageBox.information(self, "Success", "Instruments refreshed successfully")
            else:
                QMessageBox.warning(self, "Error", "Failed to refresh instruments")
        else:
            QMessageBox.warning(self, "Error", "Please login first")
    
    def verify_connection(self):
        """Verify API connection"""
        if self.auth_handler.is_authenticated():
            success, message = self.auth_handler.verify_connection()
            if success:
                QMessageBox.information(self, "Connection", "API connection verified successfully")
            else:
                QMessageBox.warning(self, "Connection Error", f"Connection verification failed: {message}")
        else:
            QMessageBox.warning(self, "Error", "Please login first")
    
    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(self, "About", 
                         "Kotak Neo Trading Bot\n\n"
                         "A professional real-time options trading application\n"
                         "for the Indian stock market (NSE F&O).\n\n"
                         "Features:\n"
                         "• Real-time price monitoring\n"
                         "• Automated trading with stop-loss/profit targets\n"
                         "• Portfolio and trade management\n"
                         "• Professional Windows interface")
    
    # Signal handlers
    @pyqtSlot(dict)
    def on_tick_received(self, tick_data):
        """Handle incoming tick data"""
        # Update watchlist prices
        self.symbol_widget.update_price(tick_data)
        
        # Check exit conditions for active trades
        self.order_manager.check_exit_conditions(tick_data)
    
    @pyqtSlot(bool, str)
    def on_connection_status(self, connected, message):
        """Handle WebSocket connection status"""
        if connected:
            self.status_bar.showMessage(f"WebSocket Connected - {message}")
        else:
            self.status_bar.showMessage(f"WebSocket Disconnected - {message}")
    
    @pyqtSlot(str)
    def on_websocket_error(self, error_message):
        """Handle WebSocket errors"""
        logger.error(f"WebSocket error: {error_message}")
    
    @pyqtSlot(dict)
    def on_order_placed(self, order_info):
        """Handle order placed event"""
        logger.info(f"Order placed: {order_info['trading_symbol']} {order_info['transaction_type']}")
    
    @pyqtSlot(dict)
    def on_order_executed(self, execution_info):
        """Handle order execution event"""
        logger.info(f"Order executed: {execution_info['trading_symbol']} at ₹{execution_info['executed_price']}")
    
    @pyqtSlot(dict)
    def on_trade_completed(self, trade_info):
        """Handle trade completion event"""
        pnl = trade_info['pnl']
        logger.info(f"Trade completed: {trade_info['trading_symbol']} P&L: ₹{pnl:.2f}")
    
    @pyqtSlot(str)
    def on_order_error(self, error_message):
        """Handle order errors"""
        logger.error(f"Order error: {error_message}")
    
    @pyqtSlot(str)
    def on_portfolio_error(self, error_message):
        """Handle portfolio errors"""
        logger.error(f"Portfolio error: {error_message}")
    
    def closeEvent(self, event):
        """Handle application close event"""
        # Cleanup
        self.websocket_client.disconnect()
        self.portfolio_manager.stop_monitoring()
        self.order_manager.shutdown()
        
        logger.info("Application closing")
        event.accept()

def main():
    """Main application entry point"""
    app = QApplication(sys.argv)
    app.setApplicationName("Kotak Neo Trading Bot")
    app.setApplicationVersion("1.0")
    
    # Create and show main window
    window = TradingApplication()
    window.show()
    
    # Start event loop
    sys.exit(app.exec_())
