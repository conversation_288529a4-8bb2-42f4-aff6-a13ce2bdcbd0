2025-07-08 14:58:15,449 - TradingBot - INFO - info:71 - Test log message at 2025-07-08 14:58:15.449408
2025-07-08 15:08:41,304 - TradingBot - WARNING - warning:75 - Could not load instruments - some features may not work
2025-07-08 15:08:41,326 - TradingBot - INFO - info:71 - Order manager initialized
2025-07-08 15:08:41,326 - TradingBot - INFO - info:71 - Portfolio manager initialized
2025-07-08 15:09:40,599 - TradingBot - WARNING - warning:75 - Could not load instruments - some features may not work
2025-07-08 15:09:40,605 - TradingBot - INFO - info:71 - Order manager initialized
2025-07-08 15:09:40,605 - TradingBot - INFO - info:71 - Portfolio manager initialized
2025-07-08 15:09:40,912 - TradingBot - INFO - info:71 - UI setup completed
2025-07-08 15:09:40,913 - TradingBot - INFO - info:71 - Signal connections setup completed
2025-07-08 15:20:51,349 - TradingBot - INFO - info:71 - Neo API client initialized successfully
2025-07-08 15:20:51,349 - TradingBot - INFO - info:71 - Requesting OTP for mobile: +919999977894
2025-07-08 15:20:52,284 - TradingBot - INFO - info:71 - OTP request sent successfully
2025-07-08 15:29:27,837 - TradingBot - INFO - info:71 - Neo API client initialized successfully
2025-07-08 15:29:27,837 - TradingBot - WARNING - warning:75 - Could not load instruments - some features may not work
2025-07-08 15:29:27,841 - TradingBot - INFO - info:71 - Order manager initialized
2025-07-08 15:29:27,841 - TradingBot - INFO - info:71 - Portfolio manager initialized
2025-07-08 15:29:28,235 - TradingBot - INFO - info:71 - UI setup completed
2025-07-08 15:29:28,235 - TradingBot - INFO - info:71 - Signal connections setup completed
2025-07-08 15:36:06,542 - TradingBot - INFO - info:71 - Portfolio monitoring stopped
2025-07-08 15:36:06,543 - TradingBot - INFO - info:71 - All scheduled orders cancelled
2025-07-08 15:36:06,545 - TradingBot - INFO - info:71 - Order manager shutdown
2025-07-08 15:36:06,545 - TradingBot - INFO - info:71 - Application closing
2025-07-08 15:39:01,305 - TradingBot - INFO - info:71 - Neo API client initialized successfully
2025-07-08 15:39:01,305 - TradingBot - WARNING - warning:75 - Could not load instruments - some features may not work
2025-07-08 15:39:01,305 - TradingBot - INFO - info:71 - Order manager initialized
2025-07-08 15:39:01,321 - TradingBot - INFO - info:71 - Portfolio manager initialized
2025-07-08 15:39:01,677 - TradingBot - INFO - info:71 - UI setup completed
2025-07-08 15:39:01,677 - TradingBot - INFO - info:71 - Signal connections setup completed
2025-07-08 15:40:36,390 - TradingBot - INFO - info:71 - Neo API client initialized successfully
2025-07-08 15:40:36,390 - TradingBot - WARNING - warning:75 - Could not load instruments - some features may not work
2025-07-08 15:40:36,396 - TradingBot - INFO - info:71 - Order manager initialized
2025-07-08 15:40:36,397 - TradingBot - INFO - info:71 - Portfolio manager initialized
2025-07-08 15:40:36,711 - TradingBot - INFO - info:71 - UI setup completed
2025-07-08 15:40:36,711 - TradingBot - INFO - info:71 - Signal connections setup completed
2025-07-08 15:40:43,666 - TradingBot - INFO - info:71 - Requesting OTP for mobile: +919999977894
2025-07-08 15:40:45,031 - TradingBot - INFO - info:71 - OTP sent successfully to mobile
2025-07-08 15:42:14,300 - TradingBot - INFO - info:71 - Completing login with OTP...
2025-07-08 15:42:14,302 - TradingBot - INFO - info:71 - Completing 2FA with OTP
2025-07-08 15:42:15,340 - TradingBot - INFO - info:71 - Session saved successfully
2025-07-08 15:42:15,340 - TradingBot - INFO - info:71 - Login successful - 2FA completed
2025-07-08 15:42:15,341 - TradingBot - INFO - info:71 - Login successful
2025-07-08 15:42:15,341 - TradingBot - INFO - info:71 - Downloading instrument master file...
2025-07-08 15:42:15,418 - TradingBot - ERROR - error:79 - Failed to download instruments: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/scrips/master?exchange_segment=nse_fo (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EE7D0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:42:15,419 - TradingBot - WARNING - warning:75 - Could not load instruments - some features may not work
2025-07-08 15:42:15,420 - TradingBot - ERROR - error:79 - Failed to connect WebSocket: 'NeoAuthHandler' object has no attribute 'sid'
2025-07-08 15:42:15,420 - TradingBot - ERROR - error:79 - WebSocket error: Failed to connect WebSocket: 'NeoAuthHandler' object has no attribute 'sid'
2025-07-08 15:42:15,422 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6ED7D0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:42:15,426 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EF9D0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:42:15,427 - TradingBot - INFO - info:71 - Portfolio monitoring started
2025-07-08 15:42:15,428 - TradingBot - ERROR - error:79 - Instruments not loaded
2025-07-08 15:42:20,452 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EE890>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:42:20,454 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EF450>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:42:25,447 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EEED0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:42:25,456 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6ECB10>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:42:30,437 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EEBD0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:42:30,440 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EC810>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:42:35,440 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EEF90>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:42:35,443 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EE810>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:42:40,451 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EC810>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:42:40,454 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EE690>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:42:45,448 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EE1D0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:42:45,451 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EFBD0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:42:50,436 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EEB90>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:42:50,439 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6ECC90>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:42:55,462 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6ED250>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:42:55,465 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EEA90>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:43:00,452 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6ED0D0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:43:00,455 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EFCD0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:43:05,446 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EE3D0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:43:05,446 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EDD50>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:43:10,539 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6ED3D0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:43:10,539 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6ED690>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:43:15,466 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EE1D0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:43:15,468 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EDB50>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:43:20,459 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6ED690>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:43:20,463 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EEA10>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:43:25,479 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EE550>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:43:25,482 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6ECB10>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:43:30,475 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EEB90>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:43:30,481 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EDA90>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:43:35,482 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6ECB10>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:43:35,482 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EFC90>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:43:40,496 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6ED7D0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:43:40,496 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EE250>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:43:45,499 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EF4D0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:43:45,503 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EFBD0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:43:50,504 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6ED310>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:43:50,506 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6ED610>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:43:55,525 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EE550>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:43:55,527 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EEA50>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:44:00,540 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6ED610>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:44:00,543 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EEA90>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:44:05,548 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EDA90>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:44:05,552 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6ED3D0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:44:10,570 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EE3D0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:44:10,577 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EE810>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:44:15,569 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6ED3D0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:44:15,569 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6ED090>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:44:20,571 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EFE50>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:44:20,574 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6ED690>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:44:25,571 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6ED7D0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:44:25,608 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EE690>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:44:30,576 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EE710>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:44:30,579 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EDB90>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:44:35,569 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EDA90>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:44:35,569 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EF6D0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:44:40,582 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EDB90>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:44:40,582 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6ECFD0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:44:45,581 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EE810>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:44:45,585 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6ED310>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:44:50,596 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EF4D0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:44:50,599 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EEF90>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:44:55,602 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6ED310>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:44:55,602 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EFB10>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:45:00,621 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EE010>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:45:00,624 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6ED610>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:45:05,629 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EFE50>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:45:05,632 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6ECB90>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:45:10,647 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EEA10>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:45:10,653 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EE650>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:45:15,652 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EE810>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:45:15,655 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EE050>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:45:20,658 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EE650>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:45:20,661 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EF8D0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:45:25,679 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EEF90>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:45:25,682 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EE710>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:45:30,679 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6ED7D0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:45:30,682 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EEB90>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:45:35,677 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EE710>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:45:35,679 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EF290>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:45:40,681 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EF990>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:45:40,684 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EDB90>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:45:45,701 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EE010>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:45:45,704 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6ED390>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:45:50,695 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EEA90>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:45:50,698 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EFCD0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:48:14,136 - TradingBot - INFO - info:71 - Loaded existing valid session
2025-07-08 15:48:17,367 - TradingBot - INFO - info:71 - Neo API client initialized successfully
2025-07-08 15:48:17,367 - TradingBot - INFO - info:71 - Downloading instrument master file...
2025-07-08 15:48:17,367 - TradingBot - ERROR - error:79 - No data received from scrip master API
2025-07-08 15:48:17,381 - TradingBot - WARNING - warning:75 - Could not load instruments - some features may not work
2025-07-08 15:48:17,382 - TradingBot - INFO - info:71 - Order manager initialized
2025-07-08 15:48:17,382 - TradingBot - INFO - info:71 - Portfolio manager initialized
2025-07-08 15:48:17,701 - TradingBot - INFO - info:71 - UI setup completed
2025-07-08 15:48:17,701 - TradingBot - INFO - info:71 - Signal connections setup completed
2025-07-08 15:48:17,720 - TradingBot - INFO - info:71 - Login successful
2025-07-08 15:48:17,720 - TradingBot - INFO - info:71 - Downloading instrument master file...
2025-07-08 15:48:17,723 - TradingBot - ERROR - error:79 - No data received from scrip master API
2025-07-08 15:48:17,724 - TradingBot - WARNING - warning:75 - Could not load instruments - some features may not work
2025-07-08 15:48:17,724 - TradingBot - INFO - info:71 - Connecting to WebSocket via Neo API client...
2025-07-08 15:48:17,725 - TradingBot - INFO - info:71 - WebSocket connection established via Neo API
2025-07-08 15:48:17,725 - TradingBot - ERROR - error:79 - Positions API error: Failed to fetch positions
2025-07-08 15:48:17,725 - TradingBot - ERROR - error:79 - Trades API error: Failed to fetch trades
2025-07-08 15:48:17,726 - TradingBot - INFO - info:71 - Portfolio monitoring started
2025-07-08 15:48:17,726 - TradingBot - ERROR - error:79 - Instruments not loaded
2025-07-08 15:48:22,730 - TradingBot - ERROR - error:79 - Positions API error: Failed to fetch positions
2025-07-08 15:48:22,732 - TradingBot - ERROR - error:79 - Trades API error: Failed to fetch trades
2025-07-08 15:48:27,759 - TradingBot - ERROR - error:79 - Positions API error: Failed to fetch positions
2025-07-08 15:48:27,760 - TradingBot - ERROR - error:79 - Trades API error: Failed to fetch trades
2025-07-08 15:48:32,753 - TradingBot - ERROR - error:79 - Positions API error: Failed to fetch positions
2025-07-08 15:48:32,754 - TradingBot - ERROR - error:79 - Trades API error: Failed to fetch trades
2025-07-08 15:48:37,754 - TradingBot - ERROR - error:79 - Positions API error: Failed to fetch positions
2025-07-08 15:48:37,755 - TradingBot - ERROR - error:79 - Trades API error: Failed to fetch trades
2025-07-08 15:48:42,754 - TradingBot - ERROR - error:79 - Positions API error: Failed to fetch positions
2025-07-08 15:48:42,755 - TradingBot - ERROR - error:79 - Trades API error: Failed to fetch trades
2025-07-08 15:48:47,746 - TradingBot - ERROR - error:79 - Positions API error: Failed to fetch positions
2025-07-08 15:48:47,747 - TradingBot - ERROR - error:79 - Trades API error: Failed to fetch trades
2025-07-08 15:48:52,746 - TradingBot - ERROR - error:79 - Positions API error: Failed to fetch positions
2025-07-08 15:48:52,747 - TradingBot - ERROR - error:79 - Trades API error: Failed to fetch trades
2025-07-08 15:48:57,767 - TradingBot - ERROR - error:79 - Positions API error: Failed to fetch positions
2025-07-08 15:48:57,769 - TradingBot - ERROR - error:79 - Trades API error: Failed to fetch trades
2025-07-08 15:49:02,772 - TradingBot - ERROR - error:79 - Positions API error: Failed to fetch positions
2025-07-08 15:49:02,773 - TradingBot - ERROR - error:79 - Trades API error: Failed to fetch trades
2025-07-08 15:49:07,769 - TradingBot - ERROR - error:79 - Positions API error: Failed to fetch positions
2025-07-08 15:49:07,770 - TradingBot - ERROR - error:79 - Trades API error: Failed to fetch trades
2025-07-08 15:49:12,773 - TradingBot - ERROR - error:79 - Positions API error: Failed to fetch positions
2025-07-08 15:49:12,773 - TradingBot - ERROR - error:79 - Trades API error: Failed to fetch trades
2025-07-08 15:49:17,786 - TradingBot - ERROR - error:79 - Positions API error: Failed to fetch positions
2025-07-08 15:49:17,788 - TradingBot - ERROR - error:79 - Trades API error: Failed to fetch trades
2025-07-08 15:49:22,790 - TradingBot - ERROR - error:79 - Positions API error: Failed to fetch positions
2025-07-08 15:49:22,790 - TradingBot - ERROR - error:79 - Trades API error: Failed to fetch trades
2025-07-08 15:49:27,779 - TradingBot - ERROR - error:79 - Positions API error: Failed to fetch positions
2025-07-08 15:49:27,781 - TradingBot - ERROR - error:79 - Trades API error: Failed to fetch trades
2025-07-08 15:49:32,789 - TradingBot - ERROR - error:79 - Positions API error: Failed to fetch positions
2025-07-08 15:49:32,791 - TradingBot - ERROR - error:79 - Trades API error: Failed to fetch trades
2025-07-08 15:49:37,779 - TradingBot - ERROR - error:79 - Positions API error: Failed to fetch positions
2025-07-08 15:49:37,779 - TradingBot - ERROR - error:79 - Trades API error: Failed to fetch trades
2025-07-08 15:49:42,792 - TradingBot - ERROR - error:79 - Positions API error: Failed to fetch positions
2025-07-08 15:49:42,792 - TradingBot - ERROR - error:79 - Trades API error: Failed to fetch trades
2025-07-08 15:49:47,786 - TradingBot - ERROR - error:79 - Positions API error: Failed to fetch positions
2025-07-08 15:49:47,786 - TradingBot - ERROR - error:79 - Trades API error: Failed to fetch trades
2025-07-08 15:49:52,797 - TradingBot - ERROR - error:79 - Positions API error: Failed to fetch positions
2025-07-08 15:49:52,798 - TradingBot - ERROR - error:79 - Trades API error: Failed to fetch trades
2025-07-08 15:49:57,792 - TradingBot - ERROR - error:79 - Positions API error: Failed to fetch positions
2025-07-08 15:49:57,793 - TradingBot - ERROR - error:79 - Trades API error: Failed to fetch trades
