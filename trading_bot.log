2025-07-08 14:58:15,449 - TradingBot - INFO - info:71 - Test log message at 2025-07-08 14:58:15.449408
2025-07-08 15:08:41,304 - TradingBot - WARNING - warning:75 - Could not load instruments - some features may not work
2025-07-08 15:08:41,326 - TradingBot - INFO - info:71 - Order manager initialized
2025-07-08 15:08:41,326 - TradingBot - INFO - info:71 - Portfolio manager initialized
2025-07-08 15:09:40,599 - TradingBot - WARNING - warning:75 - Could not load instruments - some features may not work
2025-07-08 15:09:40,605 - TradingBot - INFO - info:71 - Order manager initialized
2025-07-08 15:09:40,605 - TradingBot - INFO - info:71 - Portfolio manager initialized
2025-07-08 15:09:40,912 - TradingBot - INFO - info:71 - UI setup completed
2025-07-08 15:09:40,913 - TradingBot - INFO - info:71 - Signal connections setup completed
2025-07-08 15:20:51,349 - TradingBot - INFO - info:71 - Neo API client initialized successfully
2025-07-08 15:20:51,349 - TradingBot - INFO - info:71 - Requesting OTP for mobile: +919999977894
2025-07-08 15:20:52,284 - TradingBot - INFO - info:71 - OTP request sent successfully
2025-07-08 15:29:27,837 - TradingBot - INFO - info:71 - Neo API client initialized successfully
2025-07-08 15:29:27,837 - TradingBot - WARNING - warning:75 - Could not load instruments - some features may not work
2025-07-08 15:29:27,841 - TradingBot - INFO - info:71 - Order manager initialized
2025-07-08 15:29:27,841 - TradingBot - INFO - info:71 - Portfolio manager initialized
2025-07-08 15:29:28,235 - TradingBot - INFO - info:71 - UI setup completed
2025-07-08 15:29:28,235 - TradingBot - INFO - info:71 - Signal connections setup completed
2025-07-08 15:36:06,542 - TradingBot - INFO - info:71 - Portfolio monitoring stopped
2025-07-08 15:36:06,543 - TradingBot - INFO - info:71 - All scheduled orders cancelled
2025-07-08 15:36:06,545 - TradingBot - INFO - info:71 - Order manager shutdown
2025-07-08 15:36:06,545 - TradingBot - INFO - info:71 - Application closing
2025-07-08 15:39:01,305 - TradingBot - INFO - info:71 - Neo API client initialized successfully
2025-07-08 15:39:01,305 - TradingBot - WARNING - warning:75 - Could not load instruments - some features may not work
2025-07-08 15:39:01,305 - TradingBot - INFO - info:71 - Order manager initialized
2025-07-08 15:39:01,321 - TradingBot - INFO - info:71 - Portfolio manager initialized
2025-07-08 15:39:01,677 - TradingBot - INFO - info:71 - UI setup completed
2025-07-08 15:39:01,677 - TradingBot - INFO - info:71 - Signal connections setup completed
2025-07-08 15:40:36,390 - TradingBot - INFO - info:71 - Neo API client initialized successfully
2025-07-08 15:40:36,390 - TradingBot - WARNING - warning:75 - Could not load instruments - some features may not work
2025-07-08 15:40:36,396 - TradingBot - INFO - info:71 - Order manager initialized
2025-07-08 15:40:36,397 - TradingBot - INFO - info:71 - Portfolio manager initialized
2025-07-08 15:40:36,711 - TradingBot - INFO - info:71 - UI setup completed
2025-07-08 15:40:36,711 - TradingBot - INFO - info:71 - Signal connections setup completed
2025-07-08 15:40:43,666 - TradingBot - INFO - info:71 - Requesting OTP for mobile: +919999977894
2025-07-08 15:40:45,031 - TradingBot - INFO - info:71 - OTP sent successfully to mobile
2025-07-08 15:42:14,300 - TradingBot - INFO - info:71 - Completing login with OTP...
2025-07-08 15:42:14,302 - TradingBot - INFO - info:71 - Completing 2FA with OTP
2025-07-08 15:42:15,340 - TradingBot - INFO - info:71 - Session saved successfully
2025-07-08 15:42:15,340 - TradingBot - INFO - info:71 - Login successful - 2FA completed
2025-07-08 15:42:15,341 - TradingBot - INFO - info:71 - Login successful
2025-07-08 15:42:15,341 - TradingBot - INFO - info:71 - Downloading instrument master file...
2025-07-08 15:42:15,418 - TradingBot - ERROR - error:79 - Failed to download instruments: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/scrips/master?exchange_segment=nse_fo (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EE7D0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:42:15,419 - TradingBot - WARNING - warning:75 - Could not load instruments - some features may not work
2025-07-08 15:42:15,420 - TradingBot - ERROR - error:79 - Failed to connect WebSocket: 'NeoAuthHandler' object has no attribute 'sid'
2025-07-08 15:42:15,420 - TradingBot - ERROR - error:79 - WebSocket error: Failed to connect WebSocket: 'NeoAuthHandler' object has no attribute 'sid'
2025-07-08 15:42:15,422 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6ED7D0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:42:15,426 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EF9D0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:42:15,427 - TradingBot - INFO - info:71 - Portfolio monitoring started
2025-07-08 15:42:15,428 - TradingBot - ERROR - error:79 - Instruments not loaded
2025-07-08 15:42:20,452 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EE890>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:42:20,454 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EF450>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:42:25,447 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EEED0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:42:25,456 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6ECB10>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:42:30,437 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EEBD0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:42:30,440 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EC810>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:42:35,440 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EEF90>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:42:35,443 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EE810>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:42:40,451 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EC810>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:42:40,454 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EE690>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:42:45,448 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EE1D0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:42:45,451 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EFBD0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:42:50,436 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EEB90>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:42:50,439 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6ECC90>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:42:55,462 - TradingBot - ERROR - error:79 - Network error fetching positions: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/portfolio/positions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6ED250>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-08 15:42:55,465 - TradingBot - ERROR - error:79 - Network error fetching trades: HTTPSConnectionPool(host='kapi.kotaksecurities.com', port=443): Max retries exceeded with url: /api/v3/orders/trades (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000230FB6EEA90>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
