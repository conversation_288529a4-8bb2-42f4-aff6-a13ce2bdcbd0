Video 1: Kotak Neo API - Introduction and Login
Timestamp: 0:00 - 0:41
(Video starts with an intro graphic for "TradeNvest")
Speaker: Hello guys, welcome back to another video of TradeNvest. Aaj ke video mein hum log baat karne wale hain Kotak Neo API ki, aur Kotak Neo API ki help se hum log algo trading kaise karte hain, woh hum log seekhenge. Is series mein, yeh ek nayi series hogi is channel par, jismein hum algo trading karna seekhenge aur Kotak API ki help se hum log ek algo bhi create karenge is series mein. So, bina koi deri kiye, video shuru karte hain. Aaj ke video mein hum log login karna seekhenge is API ki help se apne account mein. Uske baad hum log dheere-dheere is series ko aage badhayenge, different-different videos ke saath mein, hum log is series ko aage badhayenge, okay. So, bina koi deri karte, video shuru karte hain. Let's start this video.
Timestamp: 0:41 - 1:15
(The screen shows the Kotak Securities Neo Trade API page)
Speaker: So, yeh aapke saamne Kotak Securities ka Neo API ka page hai. Aap log dekh sakte hain, yahan pe humko Python ka help leke, Python SDK, yahan se hum log open kar lenge. Yeh hamara Kotak Neo ka Python SDK. Okay? Yahan pe kuch is tarike ka interface hai. To aap yahan pe dekhenge toh yahan pe apne ko pip install GitHub se hum log install kar sakte hain isko directly. To aapko Git download karna hoga apne PC par. To Git download karne ke liye, simple aapko yahan pe Git likhenge aur download for Windows, ya fir aap Mac use karte ho to Mac ke liye download kar sakte ho easily.
Timestamp: 1:15 - 2:20
(The screen shows a Jupyter Notebook)
Speaker: Git download karne ke baad aapko kya karna hai? Jaise yahan pe isne de rakha hai ki aapko yeh repository install karni hai, okay? To install karne ke liye just isko copy karna hai simple and yahan pe aapko pip install karna hai. To jaise hi yeh install ho jayega, to aapko yahan pe Git install hone ke baad hi yeh install ho payega, to aapko sabse pehle Git install kar lena hai, okay? Aur main recommend karunga ki aap log Anaconda ka use kijiye yeh sab seekhne ke liye taaki easily aapka kaam ho jaye. To yeh dekhiye yahan pe hamara Git install ho chuka hai, okay. Yahan pe aap dekh sakte hain, Git install ho chuka hai. Aur abhi abhi hum log yahan pe kya karna hai? Ab kya karna hai simple? Update karna hai to yeh use karna hai, aap dekh sakte hain documentation se. Neo API client ko hum log install kara... Neo API client ko hum log import karenge yahan pe. To Neo API client ko import karenge to Neo ka jo bhi API ke data hai woh sab humko ismein mil jayenga.
Timestamp: 2:20 - 3:57
Speaker: Okay, to yeh humne import kar liya Neo API client ko, aur uske baad from neo_api_client import NeoAPI. Okay, to hum yahan pe import kar lete hain. Aur yeh humne aise hi print karwa liya. فی الحال abhi iska humko koi use nahi hai. Toh abhi yeh... yeh mere user ID, consumer key, consumer secret, mobile number aur password hai jo humko Neo mein use hote hain. To yahan pe hum log iska use karenge aur isse hum log login karenge, okay? To yahan pe aap log dekh sakte hain ki yahan pe aapko client mein, Neo API mein, yahan pe consumer key, consumer secret aur environment jo daalna hai, UAT hai. To UAT ki jagah aapko 'prod' use karna hai. Yahan pe aap dekh sakte hain, if you specify... yahan pe 'LIVE' ki jagah yahan pe UAT tha. To yahan pe jaise aap dekh sakte ho, to yahan pe consumer key... yahan pe 'LIVE' ki jagah aapko 'PROD' daalna hai. Toh tabhi aapka correct environment work karega, okay? Toh yahan pe hum log Neo API mein aa gaye. Yahan pe humne kya kar rakha hai? Main pehle mere jo bhi consumer hai, yeh saare kar leta hoon. Main pehle isko ek baar run kar leta hoon. Uske baad hum chalte hain Neo API mein. Yahan pe humne isko copy kiya hua hai, client.login. Yahan pe dekh sakte hain, client.login. Toh yahan pe humne isko jaise hi run karenge... let's run this. Isko jaise hi run karenge, to yahan pe hamara saara data humko mil jayega. Main isko ek baar hide kar raha hoon. Is tarah ka kuch data aapko milega yahan pe, aap dekh sakte hain. Uske baad aapko yahan pe ek OTP milega. To woh OTP... woh OTP mere ko abhi mil chuka hai, to OTP mere ko... OTP main ek baar put kar deta hoon ismein. Okay, OTP mil gaya.
Timestamp: 3:57 - 5:06
Speaker: Aur ab hum yahan pe aap dekh sakte hain client.login mobile number, password. To client.session_2fa, OTP lagana hoga humko. To hum OTP ke liye kya karenge? client.session aur yeh hamara OTP aa gaya aur yeh hamara install ho gaya. Okay? Ab yahan pe humko... yahan pe humko kya-kya function diye gaye hain? To yahan pe aap dekh sakte hain, trade reports hain, trade report... yeh saare function hum next video mein dekhenge. Filhaal ke liye hum log scrip master ko dekh lenge, jahan pe saara ka saara scrip humko milegi trading karne ke liye, okay? To let's see. Yahan pe client.scrip_master ko jaise hi aap run karenge, to yahan pe aapko different-different saari cheezein mil jayegi. Yahan pe jaise cash market, BSE F&O, NSE F&O, cash market, MCX, wagera, theek hai? To yahan se simple aap NSE ki, NSE ka hi chahiye aapko cash market ka, to yahan pe aapko cash market ki link mil jayegi.
Timestamp: 5:06 - 6:19
Speaker: Ab is link ki help se hum log ko maan lijiye segment NSE cash market mein Yes Bank ka symbol chahiye, to client.search karenge yahan pe, jaise hi search karenge to aap dekh sakte hain, yahan pe jaise main isko search karunga to yeh apne ko yahan pe dikha dega, okay? To jaise hi hum yahan pe client.search_scrip karke cash market mein Yes Bank ko search karenge to yeh yahan pe humko Yes Bank ka jo bhi value hoga, woh dikha dega. Yes Bank mein kaunse-kaunse share hain, Yes Bank ka NSE mein hai, cash market mein, kya price hai, kitna turnover hai, listing date kya hai, wagera-wagera, okay? Uske baad humko chahiye cash market ki F&O ka saara data chahiye, to F&O ka saara data hum log yahan pe aise print karwa sakte hain. F&O ka data jaise hi hum print karwayenge, to yahan pe pandas ka help lenge, import pandas as pd. Hum log use kar rahe hain pandas library ka, aur yahan pe F&O mein jitne bhi instruments honge, saare ke saare apne ko mil jayenge. Jaise ki aap dekh sakte hain, yahan pe sab kuch apne ko mil chuka hai, jahan pe exposure margin, freeze quantity, wagera-wagera. Saari cheezein humko mil chuki hain. To ismein saare column hai, ek baar hum check kar lete hain kaun-kaun se column humko mil rahe hain. To hum chaahe to hume jo column chahiye woh hum use kar sakte hain, baaki ke saare columns ko hum remove bhi kar sakte hain, okay? So, yahan pe aap dekh sakte hain ki symbol hai, group hai, exchange hai, yahan pe scrip base price hai, settlement type hai, units hain, segment wagera-wagera. To yahan pe hum check karke jo humko chahiye woh hum rakh sakte hain. Uske baad jo aage ki saari cheezein hain, jo websocket wagera hai, quotes hain, subscribe hai, positions hai, trading kaise karni hai, order kaise place karna hai, woh sab hum log next video mein dekhenge. Yeh sirf aur sirf hamara login video tha jo login hum log kar chuke hain. Yahan pe login kaise karna hai main sikha chuka hoon aapko. So, is video ko yahin khatam karte hain. Next video jald hi aa jayega. Aur jaane se pehle video ko like kar dena, channel ko subscribe kar dena. Milte hain next video mein, tab tak ke liye bye.
(Video ends with an outro graphic)
Video 2: Order Placement and Option Chain Data
Timestamp: 6:19 - 7:20
(Video starts with an intro graphic for "TradeNvest")
Speaker: Hello guys, welcome back to another video of TradeNvest. Aaj ke video mein hum log baat karne wale hain ki aapko Kotak Securities ki help se order placement, order modifications kaise karne hain. Iske baad wale videos mein hum log seekhenge ki trading strategy ko kis tarike se hum log ko implement karna hai Kotak Securities ki help se. So, aaj ke video mein hum log quickly seekh lete hain ki humko Kotak API ki help se order placement kaise karne hain. So, let's start this video. Usse pehle agar aap video par naye hain, video ko like kar dena, channel ko subscribe kar dena. Let's start this video.
(The screen shows a Jupyter Notebook)
Speaker: So pichle video mein hum log ne dekha tha ki scrip master se hum log ko securities ko kaise fetch karna hai, okay? To jaise hi main NSE F&O ko yahan pe search karta hoon, to yahan pe mere ko scrip master mil jata hai jo today ka hai yaani ki aaj ke din ka, okay? Uske baad humne yeh bhi dekha tha ki is humko search scrip ko kaise search karna hai with name. To Yes Bank ko jaise humne search kiya tha pichle video mein, okay? Ab hum baat kar lete hain ki hum log ko yahan pe yeh jo humne file nikali thi aur jiske humne columns nikale the yahan se, hume kin-kin columns ki zaroorat padegi? To uski help se humne nikala ki hume chahiye trading symbol, combined symbol, lot size, expiry date, segment aur freeze quantity, okay? To yeh saari cheezein humne nikal ke yahan pe is tarike se is code ka help liya hai aur yahan pe is tarike se paste kar di, lekin isko humne ek function mein karaya hai. To similarly function ka use karenge, process_csv aur sirf hume URL ko ismein parse karna hai aur yeh hume direct symbol data de dega. To URL apna kya hai? To URL apan yahan pe kar chuke hain specify. URL hai apna securities F&O, okay? Yeh hume F&O ka data chahiye. To let's process this data and yahan pe jaise hi hum isko run karenge, to hume mil jayega symbol data, jo hume yahan pe yeh return karega.
Timestamp: 7:20 - 8:27
(The screen shows code for placing, modifying, and canceling orders)
Speaker: To abhi hum yahan pe dekhiye placement, order placement kaise karna hai? To yahan pe hum Neo API ke package, Neo API ke page par jayenge, to yahan pe humko dikhega ki client.place_order, modify_order, cancel_order karna hai, order ko order ID ki help se cancel karna hai, orders ke report chahiye apne ko ki kaunsa order cancel hua hai, kaunsa order place ho chuka hai, order ki history chahiye to bhi mil jayegi, trade report bhi milegi aur yeh sabhi... yeh saari cheezein positions, holdings wagera wagera, okay? So let's go. Yahan pe humko order place karna hai, to maine kya kiya? client.place_order, NSE Cash Market mein order place kar raha hoon, product normal, price zero, market pe place karunga. IDFC ka order place karna hai mere ko aur yahan pe jaise main isko run karunga to okay, yahan pe dikha raha hai server aur yahan pe standard code 200 dikha raha hai, okay? Ab jo mera yeh order place kiya maine, isko main modify karne ki koshish karunga, to yahan pe order modification ke liye usmein daalunga order ID. Yahan pe aap dekh sakte hain, client.modify_order mein order ID aapko place karna hai. So, order ID jaise hi main daalunga, isko run karunga, kyunki mera order place nahi hua hai, abhi market band hai, 5:43 ho rahe hain, to yahan pe hume dikha dega ki the given order status is rejected, so we can't proceed further. Toh yahan pe dikha raha hai ki abhi mere account mein available balance zero hai, to uski wajah se hume hamara order place nahi hua tha, okay? Ab similar order ko hume cancel karna hai, to cancel_order wala API ka use karenge hum log. Aur yahan pe jaise isko run karenge, to yahan pe dekhiye ki order place nahi hua tha to woh hume cancel bhi nahi hoga, okay? To aapko yeh cheez samajh mein aa gayi hogi, I hope. Ab hume orders ki report chahiye ki aaj ke hamare orders kitne hain? Toh jaise hi hum run karenge, to yahan pe dekhiye order hamara reject hua hai admin... yahan pe aap dekhenge to yeh order hamara reject ho chuka hai aur reject ke reason ka hume yeh dikha raha hai yahan pe, IDFC equity ka, okay? To order hamara yahan pe reject ho chuka hai. Similarly ab hume order history chahiye particular order ki, to order history bhi hum yahan se nikal sakte hain, order number place karenge yahan pe aur order history bhi hume mil jayegi. Jaise yahan pe dekhenge, margin exceeds, cash available zero, okay? To is tarike se order number se hum yeh bhi nikal sakte hain ki hamare order ki history kya thi, order ka kya hua humne jo order place kiya tha, okay? Uske baad trade report bhi aap... kuch nahi karna hai, trade_report okay karna hai. Okay, to trade report dikha raha hai not okay, no data. Kyunki aaj koi trade hi nahi hua mere account mein to koi data hi available nahi hai, to no data aapko yahan pe show kar rahi hai. Ab trade report mein per order wise aapko dekhna hai, to jaise maine yeh order place kiya iski trade report dekhni hai. Toh there is no trade available with the given order ID. Yeh aapko errors bhi bahut acche se aapko dikha raha hai ki kaunsa error hai, aapko kya error mil raha hai, okay? Toh yahan pe jaise trade report dekhenge order ID, to aapko error bhi dikh gaya. Ab main position check karunga, meri position kya-kya chal rahi hai. Toh position dekhe to no data, koi position nahi hai. Holdings main check karunga meri holdings kya chal rahi hai, to holdings bhi hamare paas kuch nahi hai. Uske baad main limits dekhunga, to mere kitne funds available hai, woh saara... to funds bhi hamare paas abhi account mein koi bhi fund nahi hai. To yeh account maine test purpose ke liye le rakha hai, to ismein koi fund nahi hai. Toh filhaal ke liye, to yahan pe kuch limits bhi zero hai, okay? Uske baad segment mein F&O, NSE, iski limit dekhni hai, segment-wise limit dekhni hai, to bhi aap log check kar sakte ho. Okay? Iske baad margin required dekhna hai aapko kisi instrument ko buy karne ke liye margin chahiye, to aapko jaise hi isko place karoge to aapko dikha raha hai ki yeh scrip detail not found, 50 quantity ke liye, okay? Toh jaise hi hum isko run karenge, to hume yahan pe dikha dega ki abhi aapka fund chahiye 6250 ka, uske baad 6250 ka hume margin chahiye, okay? Toh yeh humko yahan pe dikha raha hai. Similarly aap log yahan pe margin required bhi check kar sakte ho, instrument token aapko acche se daalna hai, okay? Jaise maine IDFC ka daala, theek hai? Toh is tarike se aap dekh sakte ho kitna fund aapko lagega tab jaake aap buy kar paoge, okay? Yahan pe ab maine kya kar rakha hai ki yeh Havells ka maine yahan pe token pass kiya hai. Token pass karna kaise hai? To yahan pe aap dekhenge to aapko yahan pe milega ki aap log get quotes bhi fetch kar sakte ho jo aapka market mein chal raha hai. Jisse aap log ko candle banani hai to candle banao ya fir aapko trading purpose ke liye use karna hai, SL check karne ke liye use karna hai, to woh log woh bhi kar sakte hain. Toh yahan pe aap dekh sakte hain ki yeh instrument tokens, iska main ek next video bhi banaunga, jiske video mein hum log baat karenge, isko isko acche se kaise use karna hai. Is video mein فی الحال ke liye main sirf aur sirf ek intro de raha hoon us cheez ka. Toh token maine jaise hi yeh pass karunga yahan pe instrument tokens aur jaise hi main instrument tokens ko yahan pe run karunga... to aap yahan pe dekh payenge ki humko yahan pe feeds milegi. Yeh feeds mein aap log dekh sakte hain, yahan pe jaise Havells ka yeh mere paas aa gaya aur yahan pe dusre chote-mote stocks aur hain, Yes Bank ka wagera, to iska humko yahan pe quotes mil jayenge aur yeh quotes chalte rahenge. Filhaal ke liye hum log ismein detail mein nahi jayenge. Next video mein hum log dekhenge ki quotes ko kaise update honge, auto update honge. Abhi فی الحال market close hai, to hum log yeh nahi dekh payenge. Next video mein hum log MCX ki help se yeh cheez karenge. Aur abhi aapko jaise Nifty 50 aur Nifty Bank ka jaise exchange token maine yahan pe kiya aur yahan pe maine kya kiya ki quotes ko maine exchange token ke hisaab se yahan pe jaise hi isko run karunga... to yeh run ho gaya. Ab main jaise hi Nifty ko run karunga to mujhe dikhayega ki Nifty ka aaj ka closing price hai, previous day close tha 21453 aur aaj ka 302 point ke change ke saath mein Nifty gira hai. Okay? Similarly Bank Nifty ka bhi aap neeche dekh sakte hain. To is tarike se ab hume ek chahiye apna... Nifty ka message zero, last traded price nikala yahan se maine aur isko maine type kiya aur ab main isse mere ko chahiye ki abhi kya price chal raha hai Nifty ka. Toh yahan pe dekhiye abhi price chal raha hai 21150. Toh is tarike se aap log yeh cheezein use kar sakte ho apne code ko banane ke liye, apne trading setup ko live karne ke liye... apne trading strategy ko live karne ke liye Kotak Neo API ki help se. So, hum log next video mein dekhenge ki hum log ko kaise... quote API ka use karna hai aur quotes ko kaise hume fetch karna hai, okay? Is video mein bas itna hi, maine orders ke related saari terms aapko samjha di hain. Next video mein hum... dheere-dheere hum jaise-jaise aage badhte jayenge waise-waise hum in saare terms ka use lenge to uske baad aapka phir se revise ho jayega. So, I hope video aapko pasand aaya hoga. Video pasand aaya to video ko like kar dena, channel ko subscribe kar dena, milte hain next video mein tab tak ke liye bye.
(Video ends)
Video 3: Kotak Neo API - Option Chain and Live Data
Timestamp: 0:00 - 0:34
(Video starts with an intro graphic for "TradeNvest")
Speaker: Hello guys, welcome back to another video of TradeNvest. Aaj ke video mein hum Kotak Securities ki help se fetch karenge option chain ko, aur hum log dekhenge ki aap log ATM ka data kaise fetch kar sakte ho. Aaj hum log 'quotes API' ka use karenge aur 'websocket API' ka use karenge hum log next video mein, okay? Toh only quotes ko dikha ke aapko help karunga ki quotes se aap log kaise fetch kar sakte ho Kotak Neo mein LTP kaise fetch ho sakti hai, woh main aaj dikhaunga aapko. So guys, video shuru karein, usse pehle, agar aap channel par naye hain, toh video ko like kar dena aur channel ko subscribe zaroor se kar lena, okay? So let's start this video.
Timestamp: 0:34 - 1:33
(The screen shows the GitHub page for the Kotak Neo API)
Speaker: So guys, yeh hamara Kotak Neo ka jo apna GitHub page hai, yahan pe humko... neeche aap dekhnge to table of contents mein aapko yahan pe... quotes... yahan pe aapko 'quotes API' milti hai. Voh aap dekhenge yahan pe, to is tarike se quotes API humko milti hai. Yahan pe hume quotes chahiye honge, to quotes humko... yeh is tarike se humko quotes provide karenge. Yahan pe quotes likh ke instrument token fetch karenge, to hum log quotes le sakte hain. So, isko kaise use karna hai? Woh maine pichle video mein ek baar chota sa demo diya tha, lekin aaj hum isko full-fledged tarike se use karke dekhenge. So, let's start.
(The screen switches to a Jupyter Notebook with a dark theme)
Speaker: Toh yahan pe hamare paas jitne bhi database hai, pehle hum database bana lete hain. Taki saara ka saara data hamare paas aa jaye. Toh humne yahan pe ek symbol ka use kiya hai aur total database ko humne URL Lib ki help se combined database naam ke variable mein download kar liya. Okay? So, isko humne yahan pe is tarike se download kiya hai, toh hum dikhate hain combined database aapko. Jaise hi run karenge, yahan pe aapko combined database mil jayega, kuch is tarike se. Okay? Total yahan pe 1,76,822 symbols hain.
Timestamp: 1:33 - 2:42
(The speaker scrolls through the code in the notebook)
Speaker: Yeh apna combined database ho gaya. Ab yahan pe humko chahiye ki quotes ke liye hume koi bhi instrument token chahiye hoga, toh woh hum kaise fetch karenge? Toh uske liye maine yahan pe ek bana rakha hai... function banaya. Toh iska use karke hum log karenge. Simple yahan pe output list aayegi aur yahan pe humko Nifty ka chahiye, to Nifty ko jaise hi main... run karunga to yahan pe Nifty ka token aa gaya aur exchange segment kaunsa hai iska, woh humko mil jayega. To yahan pe jaise Nifty ka chahiye, ab hume ko Bank Nifty ka chahiye... toh yahan pe main Bank Nifty likhunga aur yeh humko dikha dega ki Bank Nifty ka aapka token 26009 hai aur exchange segment jo hai aapka NSE Cash Market hai, okay? Similarly agar humko ACC ka chahiye... toh yahan pe hum ACC likhenge, toh ACC ka token humko mil jayega, okay? So yeh to kuch tarika ho gaya aapko tokens lene ka, okay? Taki aap usko get quotes mein daal ke aap log LTP fetch kar sako, okay? So, is tarike se aap log kar sakte ho.
Timestamp: 2:42 - 4:06
(The speaker continues to explain the code)
Speaker: Uske baad hum log chalte hain hamare... option chain wale part pe, quotes wale part pe ki quotes humko kaise milenge. Okay? Yeh filter symbols ko filter karne ke liye, koi bhi expiry date ke humko particular expiry date ke symbols chahiye, to woh hum filter karne ke liye iska use karenge, filter symbols, okay? Symbol data... now ab aa jate hain hum symbol data pe. Symbol data ka yeh URL wise aap log ko agar symbol data chahiye, yaani ki sirf NSE cash market ka chahiye, to aap is function ka use kar sakte ho. Filhaal humko iska koi use nahi hai. So, yahan pe hum aa jate hain aur hume yahan pe chahiye... token, okay? Token chahiye? Token chahiye kiska? Nifty ka. Yeh aa gaya hamare paas Nifty ka token. Toh is baar hum log yahan pe Bank Nifty ka token lete hain.
(The speaker runs cells in the notebook)
Speaker: Token ko ab hume kya karna hai? Instrument token mein pass karna hai aur index hamara false rahega. Jaise hi hum instrument token mein pass karenge yahan pe hume... aur jaise hi hum LTP ko run karenge, yahan pe hume Bank Nifty ka saara kuch mil jayega. Yahan pe aap log dekh sakte hain, Bank Nifty ka abhi jo LTP kya chal raha hai, woh bhi, open, high, low, close kya chal raha hai, woh bhi. Yeh hamare Nifty ka hai. Toh filhaal hum Nifty le lete hain, kyunki Bank Nifty mein mere hisab se yeh wrong data show kar raha hai humko. So let's take Nifty. Nifty mil jayegi.
Timestamp: 4:06 - 5:58
Speaker: Iske saath hi aap log ko agar... zyada data chahiye, maan lo, aapko particular symbol ka data nahi chahiye, aapko bahut saare symbols ka data chahiye, to woh bhi aap log kar sakte hain. Uske liye aapko simple kya karna hai ki yahan pe data ko likhna hai aur uske baad aapko token pass karna hai, okay? Woh main aapko aage dikhata hoon, woh kaise karenge, theek hai? Yeh humko LTP mil gayi. Ab inko ATM chahiye, toh ATM us LTP se hum log kaise nikalenge? Yahan pe humne ATM nikala aur yahan pe hum simple... yeh hume 21800 ka ATM mil gaya, okay? Isse hume symbol banana hai filhaal, to ek symbol banaunga main 21800 CE, theek hai? Symbol bana diya. Symbol banaya, ab hume aur bhi symbol chahiye honge, obviously, kyunki hume option chain banani hai, to option chain banane ke liye humko different-different symbols chahiye honge. Toh yahan pe humne original symbol ko ATM symbol rakha aur uske baad doosre-doosre symbol bhi banaye. Toh yahan pe hum dekhte hain, is function ki help se... yahan pe humne total symbol bana diye 21800 CE, 21800 PE. Yahan pe aap dekh sakte hain 21800 CE, 21800 PE, yeh saare symbols aa gaye. Symbols aane ke baad hum filter karenge in symbols ko, jo hamare combined symbols mein jo hamare yeh wale symbol hain ki nahi? Toh usko filter karenge. Okay? Pehle hume filter symbols chahiye honge. Filtered... in symbols ko hume filter bhi to karna hoga, to woh cheez humne nahi kari hai yahan pe.
Timestamp: 5:58 - 7:25
(The speaker debugs and adjusts the code)
Speaker: Combined database mein hamara saara data hai, okay guys? Combined database mein hamara saara data hai, hume sirf filtered symbol chahiye. Okay, yahan pe hume symbol bhi aur expiry date bhi define karni padegi. Toh woh cheezein main ek baar yahan se copy karke le aata hoon mere function se, kyunki ek baar mere ko yeh cheez... define karni aapko zaroori hai samjhani. Aap log baaki samajh nahi payenge yeh cheez kaise kya ho raha hai yahan pe system mein, okay? So, yahan pe hum log ne symbol nahi kiya tha... toh symbol data, aur yeh hamara ho gaya, okay? Let's run this. Toh yeh aa gaya hamara symbol, filtered symbol. Total Nifty ke 505 symbol hume mile hain, okay? Hum ATM ke neeche wale 20 aur upar wale 20 symbol hi lenge hamare option chain ke liye, okay? Yeh hamare symbol ho gaye aur yeh hamare ho gaye ATM ke upar wale symbol aur neeche wale, total 80 symbols hume mile... 82 symbols ho gaye.
Timestamp: 7:25 - 8:27
Speaker: Ab in symbols se humko kya chahiye? Token chahiye. Toh token kaise nikalenge? Toh humne ek function banaya, create_exchange_tokens ka jo hume in saare symbols ke token humko provide karwayega. Toh yeh token isne humko kuch is tarike se provide karwa diye. Dekhiye aap log, yahan pe yeh saare tokens isne humko provide karwa diye hain. Aise aapko tokens mil jayenge. Ab aapko inke options ke quotes chahiye, in tokens ke related, to yeh quotes humko... yeh dekhiye yahan pe humko yeh saare quotes mil gaye. Similarly, yahan pe dekhiye yeh hai 22200 PE ka quote. Yeh hai 21500 CE ka quote hai. Toh aise aapko quotes mil jayenge. Toh jaise hi humko direct option chain fetch karni hai... ab yahan pe maine kuch cheezein abhi chhod di hain. Jaise main sirf Nifty ki option chain fetch karne ke liye yahan pe dikha raha hoon. In saare 50 aur yeh saari cheezein maine yahan pe pre-fill kar diya hai. Maine isko variable nahi rakha hai. Variable rakhne ke liye humko kuch cheezein changes karne padenge, lekin فی الحال ke liye is video mein hum zyada lamba nahi karenge aur isko koi change nahi karte hue, sirf Nifty ke liye use karenge, okay? Toh is function ko run karenge. Jaise hi yeh function run hoga, uske baad is function mein get option chain, hume Nifty ki option chain chahiye aur expiry date aapko combined database mein se dhoond leni hai. Toh expiry date hai 28-12-2023, aaj ki. Let's run this aur jaise hi isko run karenge, yeh humko option chain de dega. Is option chain ka use aap log kar sakte ho premium-based symbol dhoondne mein. Koi jaise aapko 60 se upar ka symbol sell karna hai to aap log yahan pe yeh wala symbol aap fetch kar sakte ho. Similarly aapko kai aur cheezein bhi chahiye hongi, to woh cheezein bhi aap log yahan se le sakte ho aur is symbol ke base pe, is symbol ka token kya hai, agar aapko chahiye hoga to woh bhi aap yahan se fetch kar sakte ho. Filhaal ke liye humne token ko ismein add nahi kiya hai lekin aap log add kar sakte ho. Toh is tarike se aap log get quotes API ka use karke aap log different-different option chain ke quotes bhi fetch kar sakte ho. So I hope yeh video aapko pasand aaya hoga. Next video mein hum log baat karenge websocket ki. Websocket se data ko aapko kaise fetch karna hai aur kaise aapko usko use karna hai apne code mein, okay? So I hope video aapko pasand aaya hoga. Video pasand aaya to video ko like kar dena, channel ko subscribe kar dena, milte hain next video mein tab tak ke liye bye.
(Video ends with an outr
Of course. Here is the full transcription for the final video in the series.

### Video 4: Kotak Neo API - WebSocket for Live Data

**Timestamp: 0:00 - 0:17**

**(Video starts with an intro graphic for "TradeNvest")**

**Speaker:** Hello guys, welcome back to another video of TradeNvest. Aaj ke video mein hum log baat karenge ki Kotak API ki help se aapko WebSocket ka data kaise fetch karna hai aur us data ko use kaise karna hai. So, bina koi deri kiye video shuru karte hain. Usse pehle agar aap channel par naye hain, to video ko like kar dena, channel ko subscribe kar dena. Let's start this video.

**Timestamp: 0:17 - 1:17**

**(The screen shows a Jupyter Notebook in Visual Studio Code with a dark theme)**

**Speaker:** So guys, yahan pe hamare paas yeh WebSocket ka code hai. Yeh code aapko mil jayega Kotak Neo API ki website pe. Yahan pe humne `live_data` naam ki dictionary banai hai. Usmein humne `global live_data` define kiya hai, aur `for i in message`, yaani ki jo hume WebSocket se data receive hoga, woh `message` variable mein receive hota hai. So, `message` mein hum live data token ke naam se LTP fetch kar rahe hain. Aur agar exception aata hai, to hum error message ko as an exception print karwa denge, okay? Aur yeh `on_error` yahan pe hamara login procedure hai. Main login already kar chuka hoon, to main yahan pe aapko nahi dikha raha hoon login karke. Yahan pe hamara token hai, aur yeh 2FA main login already kar chuka hoon aur session bhi start kar chuka hoon. So, isko hum nahi kar rahe hain is baar. Aur jaise hi hum yahan pe... yahan pe hamara yeh combined database hai jo apna database milta tha.

**Timestamp: 1:17 - 2:03**

**(The speaker scrolls through the code and explains the recent API updates)**

**Speaker:** Toh hum isko bhi run kar lete hain ek baar. And ab yahan pe main kya karta hoon ki apni Neo API client ko ab yahan pe hum... subscribe karte hain. Neo API ko jaise hi hum karenge, to humko yahan pe access token mil gaya aur hume yahan pe apna token mil jayega. Toh token yahan pe hai 1172. 1172 jaise hi hum token daalenge yahan pe, to hamara login ho jayega. And... uske baad jo start mein jo data hai, woh yahan pe data milega. Yahan pe jo change jo kiya hai unhone, yahan pe `neo_fin_key` aur `access_token` dono `None` kar diya hai. Aur yahan pe jo `on_message`, `on_error` wagera jo yahan pe dekh pa rahe hain, yeh saari cheezein thi, woh saari cheezein unhone hata di hain. Toh hum isko bhi run kar lete hain ek baar. And ab yahan pe `client.scrip_master` ko access karte hain. Toh... ab hume file paths mil rahe hain. Toh ek baar hum combined database ko access kar lete hain, uske baad WebSocket kaise work karega, woh bhi dikha deta hoon, okay? Yahan pe combined database access ho chuka hai.

**Timestamp: 2:03 - 3:00**

**(The speaker explains how to get tokens and subscribe to the WebSocket)**

**Speaker:** Uske baad main futures ka data filter kar leta hoon ek baar. Aur futures ka jo data hai, filter karne ke baad... uske baad main futures ke token generate karta hoon yahan pe. Yeh dekhiye futures ke token generate ho gaye yahan pe saare. Uske baad main kya karta hoon, `client.subscribe` aur future tokens... ek baar mere ko... stream open karni padegi. Toh instrument tokens ki jagah pe main yahan pe future tokens kar deta hoon. Okay? And isko jaise hi hum run karenge yahan pe, to hamare... feed subscribe ho jayegi. Feed subscribe ho chuki hai hamari, aur hume feed milni start ho jayegi hamari `live_data` mein. Previous data ko hum check karte hain to yahan pe dekhiye feed mil rahi hai aapko. LTP zero hai, فی الحال. OI wagera saara. To yahan pe previous data mein aur live data aap check karenge, to live data mein bhi aapko feed mil rahi hai, theek hai? To is tarike se aap log yahan pe... hum log aisa karte hain... Nifty ko subscribe karte hain. Get tokens for Nifty, `tok`. Uske baad Nifty ka data aa gaya. Uske baad yahan pe phir se hum isko 'tok' hi kar lete hain. Aur isko run karte hain. Toh yahan pe hume... previous data mein bhi data mil jayega 26000 Nifty ka data mil raha hai, aur live data mein bhi hume 26000 aur Nifty ka trading symbol wagera mil raha hai, okay? Toh is tarike se aap log naye API ki help se bhi login kar sakte hain. Koi bhi changes nahi hai. Saara changes ko main website par daal dunga. I hope yeh video aapko pasand aayi hogi.

**Timestamp: 3:00 - 4:22**

**(The speaker demonstrates the WebSocket live feed for different instruments)**

**Speaker:** Toh yahan pe hum dekhte hain live data... Jaise hi hum isko run karenge, yeh dekhiye yahan pe 5886 abhi فی الحال price chal raha hai. Next, jaise hi LTP receive nahi hua hai, to isne message kar diya. Jaise hi phir se run karunga, 5886 hi abhi chal raha hai. Jaise hi yeh thoda sa change hoga data, automatic yahan pe main jaise... yeh dekhiye 5891 ho gaya, to hume naya data receive ho gaya. Ab main ek naya token subscribe karta hoon. Yahan pe 'tok' naam se humne yahan pe Bankex ko subscribe karte hain. Let's see. Bankex ki jagah maan lijiye hume ek baar... main ek Gold ka... dekh lete hain abhi Gold ka agar hume milta hai to. Gold 24 Jan futures ke quotes chahiye, okay? Toh main jaise isko run karunga, to yahan pe mere ko yeh mil jata hai. Similarly, mujhe yahan pe kya karna hai? Ab mere ko maan lijiye crude oil 24 Jan futures ke quotes chahiye, okay? Toh main jaise isko run karunga, to yahan pe mere ko yeh mil jata hai. Similarly, main isko print karwata hoon. Okay, Gold ka ek baar mere ko symbol dekhna padega, iska symbol kya hai. Toh main yahan pe Gold 24 Feb ke futures ko fetch karta hoon. Let's see. Okay... aur yahan pe hum jaise hi run karenge, to yeh hume Gold ka token mil gaya. Abhi hum is token 2 ko yahan pe copy karte hain, Ctrl+C and yahan pe hum yahan pe isko paste karte hain. Let's paste this.

**Timestamp: 4:22 - 5:28**

**(The speaker demonstrates subscribing to multiple tokens and using the live data)**

**Speaker:** Toh paste kar diya humne. Ab hum live data fetch karenge, to yahan pe hume do token ka data mil raha hai. Yeh Gold ka hai 62775 aur yeh hamara... yeh data hai, okay? Jaise hi hum isko run karenge, to yeh 5886 abhi فی الحال chal raha hai. Next message mein LTP receive nahi hua hai. Jaise hi hum isko run karenge, 5889 ho gaya. Toh yeh hume dikha raha hai yahan pe abhi ka hamara data, okay? Let's... okay, ab ek aur naya token. Yahan pe 'tok' naam se humne yahan pe Bankex ko subscribe karte hain, dhoondte hain. Toh Bankex ka token kya aa raha hai? Bankex ka token hai 12. Toh abhi Bankex ko subscribe karne ke liye tok mein yahan pe subscribe karenge. Subscribe ho chuka hai hamara. Abhi jaise hi live data pe fetch karenge, to yahan pe hume Bankex ka data bhi mil jayega. So guys, is tarike se aap log... aur yahan pe jaise hume live data mein se... is wale token ka, particular token ka hume data hi chahiye sirf single token ka, to yahan pe jaise aap isko run karenge... yahan pe jaise hi aap isko run karenge, yahan pe aapko crude oil ka single data mil jayega. Isko aap log compare karke bhi use kar sakte ho, okay? So I hope video aapko pasand aaya hoga. Yahan pe 5887 hai, ek baar main ek code likh ke dikhata hoon aapko. `gk > 5888` `print('yes')` `else print('no')`. Okay? Yahan pe hum `gk` ka value فی الحال hum print karwa ke dekh lete hain `gk` ka value kya hai. 5916 chal raha hai. Toh let's run this. Toh 'yes' dikha raha hai abhi hamara. Toh hum isko 5916, 59916 hai na, 5920 kar lete hain. Toh abhi hume dikhayega yahan pe 'no', okay? Aur jaise hi value `gk` ka... 5917 hua abhi, 5917 chal raha hai فی الحال. Jaise hi yeh 5917 isko while loop mein run karenge, maan lijiye...

**Timestamp: 5:28 - 5:58**

**(The speaker provides a concluding summary)**

**Speaker:** Toh ek baar, isko فی الحال ke liye chhod dete hain. Okay, yahan pe aapko dikha raha hai 5917 iski value hai فی الحال ke liye. Toh main jaise hi run karunga, 5909 ho gaya. Toh abhi 5909 hai to main yahan pe less than kar deta hoon. Toh jaise hi less than karunga to mera comparison ho jayega 'yes'. Dekhiye aapko 'yes' dikha raha hai. So is tarike se aap log WebSocket data ka use kar sakte hain aur comparison bhi kar sakte hain. So I hope video aapko pasand aaya hoga. Video pasand aaya to video ko like kar dena, channel ko subscribe kar dena, milte hain next video mein tab tak ke liye bye.

**(Video ends with an outro graphic)**
Video 3: Kotak Neo API - WebSocket Login and Live Data
Timestamp: 0:00 - 0:22
(Video starts with an intro graphic for "TRADENVEST")
Speaker: Hello guys, welcome back to another video of TradeNvest. Aaj ke video mein hum log baat karenge ki aapko Kotak Neo API, jo unhone update kiya hai, uske baad aapko phir se login kaise karna hai aur WebSocket API ka use kaise karna hai. So, without wasting any time, video shuru karte hain. Usse pehle agar aap channel par naye hain, toh video ko like kar dena, channel ko subscribe kar dena. Let's start this video.
Timestamp: 0:22 - 1:09
(The screen shows a Jupyter Notebook titled "Arbitrage.ipynb" in what appears to be VS Code)
Speaker: So, sabse pehle unhone jo update kiya hai, toh aap log jo unki website par jaoge toh kuch changes aapko milenge GitHub ki website par. Toh un changes ke hisaab se maine apne... apne system mein updated API ko install kar liya hai aur main yahan pe jaise hi Neo API client ko import karta hoon, toh yeh yahan pe import ho rahi hai, okay? Python environment mein hume yahan pe import karta hoon Neo API client ko, toh yahan pe yeh import ho rahi hai. Toh jab tak yeh connect hota hai tab tak aap channel ko subscribe zaroor se kar lena, okay? Okay, toh yahan pe connect ho chuka hai aur yeh yahan pe run kar raha hai. Aap dekh sakte hain yeh Jupyter Notebook ka extension hai VS Code ke liye. Toh aap log bhi use kar sakte hain VS Code mein.
Timestamp: 1:09 - 1:37
Speaker: Unko bhi run kar leta hoon. Aap dekh sakte hain, CK, CS, wagera-wagera, yeh saari cheezein yahan pe hai. Uske baad aapko kya karna hai? Simple yeh WebSocket ka code hai, yeh saara code main website par daal dunga, toh aap log access kar paoge wahan se. Toh yahan pe jo pehle jo data directly message mein jo hamara data aata tha, woh data ab message mein ek 'data' naam ki nayi key jo exist karti hai, uss key mein saara data hota hai. Toh is... yeh wala update bhi wahan pe hua hai. Toh isko bhi ek baar main run kar leta hoon.
Timestamp: 1:37 - 2:24
Speaker: And ab yahan pe main kya karta hoon ki apni... Neo API client ko ab yahan pe hum subscribe karte hain. Neo API ko jaise hi hum karenge, toh humko yahan pe access token mil gaya aur hume yahan pe apna token mil jayega. Toh token yahan pe hai 1172. Toh 1172 jaise hi hum token daalenge yahan pe, toh hamara login ho jayega. And, yahan pe jo start, uske baad jo start mein jo data hai, woh yahan pe data milega. Yahan pe jo change jo kiya hai unhone, yahan pe neo_fin_key and access_token dono None kar diya hai aur yahan pe jo on message, on error wagera jo yahan pe dekh pa rahe hain, yeh saari cheezein... woh saari cheezein unhone hata di hai. Toh hum isko bhi run kar lete hain ek baar.
Timestamp: 2:24 - 3:17
(The host runs the code cells to fetch the scrip master files)
Speaker: And ab yahan pe isko run karte hain, toh yeh hamara combined database hai jo apna database milta tha. Toh file paths jo chahiye humko, client.scrip_master mein file paths mil rahe hain. Toh yahan pe dikha raha hai, file paths equals to... Okay, toh humne 2FA process ko ek baar phir se hume 2FA process ko karna hoga complete. Toh maine yahan pe isko complete kar liya. And ab isko main run karta hoon. Toh phir se humko 2FA process dikha raha hai, toh ek baar phir se hum 2FA process kar lete hain. Error aa rahi hai, toh isko ek baar hume karna padega. 2318. Okay, apna yeh humne login kar liya yahan pe. Aur ab hum client master ko access karte hain. Toh... toh ab hume file paths mil rahe hain. Toh hum ek baar combined database ko access kar lete hain. Uske baad WebSocket kaise work karega, woh bhi dikha deta hoon, okay? Toh yahan pe combined database access ho chuka hai.
Timestamp: 3:17 - 4:18
Speaker: Uske baad main futures ka data filter kar leta hoon ek baar. Aur futures ka jo data hai, filter karne ke baad... uske baad main futures ke token generate karta hoon yahan pe. Toh futures ke token main generate karta hoon. Dekhiye yahan pe futures ke tokens generate ho gaye yahan pe saare. Uske baad main kya karta hoon, client.subscribe aur future tokens... ek baar mere ko stream open karni padegi. Toh instrument tokens ki jagah pe main yahan pe Fut_tokens kar deta hoon. Okay. And isko jaise hi hum run karenge yahan pe, toh hamara feed subscribe ho jayega.
Timestamp: 4:18 - 4:41
(The screen shows a stream of live data from the WebSocket)
Speaker: Okay, everything is set now. Ab hum ek baar isko run karte hain. Toh hume feed milni start ho jayegi, hamari live data mein. Toh previous_data ko hum check karte hain. Toh dekhiye feed mil rahi hai aapko. LTP zero hai فی الحال OI wagera-wagera, saara... toh yahan pe previous_data mein aur live data aap check karenge toh live data mein bhi aapko feed mil rahi hai. Toh is tarike se aap log yahan pe... hum log aisa karte hain, Nifty ko subscribe karte hain.
Timestamp: 4:41 - 5:02
Speaker: Get tokens for Nifty. Talk... uske baad Nifty ka data aa gaya. Uske baad yahan pe phir se hum isko 'tok' hi kar lete hain. Aur isko run karte hain. Toh yahan pe hume... previous_data mein bhi data mil jayega, 26000 Nifty ka data mil raha hai, aur live data mein bhi hume 26000 aur Nifty ka trading symbol wagera mil raha hai, okay? Toh is tarike se aap log naye API ki help se bhi login kar sakte hain. Koi bhi changes nahi hai, saara changes ko main website par daal dunga. I hope yeh video aapko pasand aayi hogi. Video pasand aayi toh video ko like kar dena, channel ko subscribe kar dena. Milte hain next video mein.
(Video ends with a thank you screen showing the URL www.tradenvesteasy.com)
