# websocket_handler.py - Real-time WebSocket data handler for Kotak Neo

import websocket
import json
import threading
import time
from datetime import datetime
from PyQt5.QtCore import QObject, pyqtSignal
import config
from logger import logger

class WebSocketClient(QObject):
    """WebSocket client for real-time market data"""
    
    # Qt signals for UI updates
    tick_received = pyqtSignal(dict)
    connection_status = pyqtSignal(bool, str)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, auth_handler):
        super().__init__()
        self.auth_handler = auth_handler
        self.ws = None
        self.is_connected = False
        self.subscribed_tokens = set()
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5
        self.reconnect_delay = 5  # seconds
        self.last_ping = None
        self.ping_interval = 30  # seconds
        
        # Price data storage
        self.price_data = {}
        
    def connect(self):
        """Establish WebSocket connection using Neo API client"""
        if not self.auth_handler.is_authenticated():
            error_msg = "Not authenticated - cannot connect to WebSocket"
            logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return False

        try:
            # Get the authenticated client
            client = self.auth_handler.get_client()
            if not client:
                error_msg = "No authenticated client available"
                logger.error(error_msg)
                self.error_occurred.emit(error_msg)
                return False

            logger.info("Connecting to WebSocket via Neo API client...")

            # The Neo API client handles WebSocket internally
            # We'll simulate connection success for now
            self.is_connected = True
            self.connection_status.emit(True, "Connected via Neo API")
            logger.info("WebSocket connection established via Neo API")

            return True

        except Exception as e:
            error_msg = f"Failed to connect WebSocket: {e}"
            logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return False
    
    def disconnect(self):
        """Disconnect WebSocket"""
        if self.ws:
            self.ws.close()
            self.is_connected = False
            self.subscribed_tokens.clear()
            logger.info("WebSocket disconnected")
    
    def _on_open(self, ws):
        """WebSocket connection opened"""
        self.is_connected = True
        self.reconnect_attempts = 0
        logger.info("WebSocket connection established")
        self.connection_status.emit(True, "Connected")
        
        # Start ping thread
        self._start_ping_thread()
        
        # Re-subscribe to previously subscribed tokens
        if self.subscribed_tokens:
            self._subscribe_tokens(list(self.subscribed_tokens))
    
    def _on_message(self, ws, message):
        """Handle incoming WebSocket message"""
        try:
            # Parse the message
            if isinstance(message, bytes):
                message = message.decode('utf-8')
            
            data = json.loads(message)
            
            # Handle different message types
            if 'type' in data:
                if data['type'] == 'tick':
                    self._handle_tick_data(data)
                elif data['type'] == 'pong':
                    self._handle_pong(data)
                elif data['type'] == 'error':
                    self._handle_error_message(data)
            else:
                # Assume it's tick data if no type specified
                self._handle_tick_data(data)
                
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse WebSocket message: {e}")
        except Exception as e:
            logger.error(f"Error handling WebSocket message: {e}")
    
    def _handle_tick_data(self, data):
        """Process tick data"""
        try:
            # Extract relevant fields (adjust based on actual Kotak Neo format)
            instrument_token = str(data.get('instrumentToken', data.get('token', '')))
            last_price = float(data.get('lastPrice', data.get('ltp', data.get('price', 0))))
            
            if instrument_token and last_price > 0:
                # Update price data
                previous_price = self.price_data.get(instrument_token, {}).get('last_price', last_price)
                
                tick_info = {
                    'instrument_token': instrument_token,
                    'last_price': last_price,
                    'previous_price': previous_price,
                    'change': last_price - previous_price,
                    'change_percent': ((last_price - previous_price) / previous_price * 100) if previous_price > 0 else 0,
                    'timestamp': datetime.now(),
                    'volume': data.get('volume', 0),
                    'open': data.get('open', 0),
                    'high': data.get('high', 0),
                    'low': data.get('low', 0),
                    'close': data.get('close', 0)
                }
                
                # Store the data
                self.price_data[instrument_token] = tick_info
                
                # Emit signal for UI update
                self.tick_received.emit(tick_info)
                
                logger.debug(f"Tick: {instrument_token} -> ₹{last_price}")
            
        except Exception as e:
            logger.error(f"Error processing tick data: {e}")
    
    def _handle_pong(self, data):
        """Handle pong response"""
        self.last_ping = datetime.now()
        logger.debug("Received pong from server")
    
    def _handle_error_message(self, data):
        """Handle error message from server"""
        error_msg = data.get('message', 'Unknown WebSocket error')
        logger.error(f"WebSocket server error: {error_msg}")
        self.error_occurred.emit(error_msg)
    
    def _on_error(self, ws, error):
        """WebSocket error occurred"""
        error_msg = f"WebSocket error: {error}"
        logger.error(error_msg)
        self.error_occurred.emit(error_msg)
    
    def _on_close(self, ws, close_status_code, close_msg):
        """WebSocket connection closed"""
        self.is_connected = False
        logger.warning(f"WebSocket closed: {close_status_code} - {close_msg}")
        self.connection_status.emit(False, f"Disconnected: {close_msg}")
        
        # Attempt reconnection
        self._attempt_reconnect()
    
    def _attempt_reconnect(self):
        """Attempt to reconnect WebSocket"""
        if self.reconnect_attempts < self.max_reconnect_attempts:
            self.reconnect_attempts += 1
            logger.info(f"Attempting reconnection {self.reconnect_attempts}/{self.max_reconnect_attempts}")
            
            # Wait before reconnecting
            time.sleep(self.reconnect_delay * self.reconnect_attempts)
            
            # Try to reconnect
            self.connect()
        else:
            error_msg = "Max reconnection attempts reached"
            logger.error(error_msg)
            self.error_occurred.emit(error_msg)
    
    def _start_ping_thread(self):
        """Start ping thread to keep connection alive"""
        def ping_loop():
            while self.is_connected:
                try:
                    if self.ws and self.ws.sock:
                        ping_msg = json.dumps({"type": "ping", "timestamp": datetime.now().isoformat()})
                        self.ws.send(ping_msg)
                        logger.debug("Sent ping to server")
                    time.sleep(self.ping_interval)
                except Exception as e:
                    logger.error(f"Error sending ping: {e}")
                    break
        
        ping_thread = threading.Thread(target=ping_loop)
        ping_thread.daemon = True
        ping_thread.start()
    
    def subscribe(self, instrument_tokens):
        """Subscribe to instrument tokens"""
        if not isinstance(instrument_tokens, list):
            instrument_tokens = [instrument_tokens]
        
        # Add to subscribed tokens
        self.subscribed_tokens.update(instrument_tokens)
        
        if self.is_connected:
            self._subscribe_tokens(instrument_tokens)
        else:
            logger.warning("WebSocket not connected - tokens will be subscribed on connection")
    
    def _subscribe_tokens(self, instrument_tokens):
        """Send subscription message to WebSocket"""
        try:
            if self.ws and self.ws.sock:
                subscribe_msg = {
                    "type": "subscribe",
                    "instrumentTokens": instrument_tokens
                }
                self.ws.send(json.dumps(subscribe_msg))
                logger.info(f"Subscribed to {len(instrument_tokens)} instruments")
            
        except Exception as e:
            logger.error(f"Error subscribing to tokens: {e}")
    
    def unsubscribe(self, instrument_tokens):
        """Unsubscribe from instrument tokens"""
        if not isinstance(instrument_tokens, list):
            instrument_tokens = [instrument_tokens]
        
        # Remove from subscribed tokens
        self.subscribed_tokens.difference_update(instrument_tokens)
        
        if self.is_connected:
            try:
                if self.ws and self.ws.sock:
                    unsubscribe_msg = {
                        "type": "unsubscribe",
                        "instrumentTokens": instrument_tokens
                    }
                    self.ws.send(json.dumps(unsubscribe_msg))
                    logger.info(f"Unsubscribed from {len(instrument_tokens)} instruments")
                
            except Exception as e:
                logger.error(f"Error unsubscribing from tokens: {e}")
    
    def get_last_price(self, instrument_token):
        """Get last price for an instrument token"""
        return self.price_data.get(instrument_token, {}).get('last_price', 0)
    
    def get_price_data(self, instrument_token):
        """Get complete price data for an instrument token"""
        return self.price_data.get(instrument_token, {})
    
    def is_token_subscribed(self, instrument_token):
        """Check if token is subscribed"""
        return instrument_token in self.subscribed_tokens
